package main

import (
	"context"
	"fmt"
	"log"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
)

func main() {
	// Test encryption service
	encryptionService, err := services.NewEncryptionService()
	if err != nil {
		log.Fatalf("Failed to create encryption service: %v", err)
	}

	// Test data
	testData := []byte("api_key=secret_key_123456")

	// Encrypt
	encrypted, err := encryptionService.Encrypt(context.Background(), testData)
	if err != nil {
		log.Fatalf("Failed to encrypt: %v", err)
	}

	fmt.Printf("Original: %s\n", testData)
	fmt.Printf("Encrypted: %s\n", encrypted)

	// Decrypt
	decrypted, err := encryptionService.Decrypt(context.Background(), encrypted)
	if err != nil {
		log.Fatalf("Failed to decrypt: %v", err)
	}

	fmt.Printf("Decrypted: %s\n", decrypted)

	// Verify
	if string(testData) == string(decrypted) {
		fmt.Println("✅ Encryption/Decryption successful!")
	} else {
		fmt.Println("❌ Encryption/Decryption failed!")
	}
}
