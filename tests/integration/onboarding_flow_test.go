package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/gorm"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"

	onboardingHandlers "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/handlers"
	onboardingModels "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	onboardingRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories/mysql"
	onboardingServices "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"

	tenantHandlers "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/handlers"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	tenantRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"

	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	userServices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"

	websiteHandlers "github.com/tranthanhloi/wn-api-v3/internal/modules/website/handlers"
	websiteModels "github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	websiteRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"

	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

var _ = Describe("Onboarding Flow Integration Tests", func() {
	var (
		db         *gorm.DB
		testHelper *helpers.TestHelper
		seeder     *seeders.MasterSeeder
		router     *gin.Engine
		logger     utils.Logger

		// Services
		authService       authServices.AuthService
		tenantService     tenantServices.TenantService
		userService       userServices.UserService
		websiteService    websiteServices.WebsiteService
		onboardingService onboardingServices.OnboardingService

		// Test data
		testUser     *authModels.User
		testTenant   *tenantModels.Tenant
		testWebsite  *websiteModels.Website
		testEmail    string
		testPassword string

		ctx context.Context
	)

	BeforeEach(func() {
		var err error

		// Setup test database
		db, err = helpers.SetupTestDatabase()
		Expect(err).NotTo(HaveOccurred())

		// Run migrations
		err = helpers.RunMigrations(db)
		Expect(err).NotTo(HaveOccurred())

		// Initialize test helper and seeder
		testHelper = helpers.NewTestHelper(db)
		logger = utils.NewLogger("test")
		seeder = seeders.NewMasterSeeder(db, logger)

		// Setup test context
		ctx = context.Background()

		// Clean and seed database
		testHelper.CleanDatabase()
		err = seeder.SeedAll(ctx)
		Expect(err).NotTo(HaveOccurred())

		// Setup services
		setupServices()

		// Setup router with handlers
		setupRouter()

		// Generate test data
		testEmail = fmt.Sprintf("<EMAIL>", time.Now().Unix())
		testPassword = "TestPassword123!"
	})

	AfterEach(func() {
		if db != nil {
			testHelper.CleanDatabase()
			sqlDB, _ := db.DB()
			sqlDB.Close()
		}
	})

	Describe("Complete Onboarding Flow", func() {
		Context("When a new user registers", func() {
			It("Should complete the full registration -> tenant creation -> website setup flow", func() {
				By("Step 1: User Registration")
				registrationData := map[string]interface{}{
					"email":                 testEmail,
					"password":              testPassword,
					"password_confirmation": testPassword,
					"first_name":            "John",
					"last_name":             "Doe",
					"terms_accepted":        true,
				}

				registrationResponse := makeAuthRequest("POST", "/auth/register", registrationData)
				Expect(registrationResponse.Code).To(Equal(http.StatusCreated))

				var regResult map[string]interface{}
				err := json.Unmarshal(registrationResponse.Body.Bytes(), &regResult)
				Expect(err).NotTo(HaveOccurred())
				Expect(regResult["success"]).To(BeTrue())

				userData := regResult["data"].(map[string]interface{})
				userID := uint(userData["user"].(map[string]interface{})["id"].(float64))
				accessToken := userData["access_token"].(string)

				By("Step 2: Verify user was created")
				var createdUser authModels.User
				err = db.First(&createdUser, userID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdUser.Email).To(Equal(testEmail))
				Expect(createdUser.Status).To(Equal(authModels.UserStatusActive))

				By("Step 3: Check onboarding progress was automatically created")
				var userProgress onboardingModels.OnboardingProgress
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("in_progress"))
				Expect(userProgress.CurrentStepOrder).To(Equal(uint(1)))

				By("Step 4: Complete onboarding steps")
				// Get the onboarding journey and steps
				var journey onboardingModels.OnboardingJourney
				err = db.First(&journey, userProgress.JourneyID).Error
				Expect(err).NotTo(HaveOccurred())

				var steps []onboardingModels.OnboardingStep
				err = db.Where("journey_id = ?", journey.ID).Order("order_position ASC").Find(&steps).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(len(steps)).To(BeNumerically(">", 0))

				// Complete each onboarding step
				for _, step := range steps {
					stepData := generateStepCompletionData(step.Type)
					stepResponse := makeOnboardingRequest("POST", "/onboarding/progress/complete-step", map[string]interface{}{
						"step_id":   step.ID,
						"user_data": stepData,
					}, accessToken)

					Expect(stepResponse.Code).To(Equal(http.StatusOK))

					var stepResult map[string]interface{}
					err := json.Unmarshal(stepResponse.Body.Bytes(), &stepResult)
					Expect(err).NotTo(HaveOccurred())
					Expect(stepResult["success"]).To(BeTrue())
				}

				By("Step 5: Verify onboarding completion")
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("completed"))
				Expect(userProgress.CompletedAt).NotTo(BeNil())

				By("Step 6: Create tenant")
				tenantData := map[string]interface{}{
					"name":        "Test Organization",
					"slug":        fmt.Sprintf("test-org-%d", time.Now().Unix()),
					"description": "Test organization for integration testing",
					"plan_id":     1,
					"billing_info": map[string]interface{}{
						"billing_email": testEmail,
						"company_name":  "Test Company",
					},
				}

				tenantResponse := makeTenantRequest("POST", "/tenants", tenantData, accessToken)
				Expect(tenantResponse.Code).To(Equal(http.StatusCreated))

				var tenantResult map[string]interface{}
				err = json.Unmarshal(tenantResponse.Body.Bytes(), &tenantResult)
				Expect(err).NotTo(HaveOccurred())
				Expect(tenantResult["success"]).To(BeTrue())

				tenantData = tenantResult["data"].(map[string]interface{})
				tenantID := uint(tenantData["id"].(float64))

				By("Step 7: Verify tenant creation and user membership")
				var createdTenant tenantModels.Tenant
				err = db.First(&createdTenant, tenantID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdTenant.Name).To(Equal("Test Organization"))
				Expect(createdTenant.Status).To(Equal(tenantModels.TenantStatusActive))

				// Check user membership
				var membership userModels.TenantMembership
				err = db.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&membership).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(membership.Status).To(Equal(userModels.TenantMembershipStatusActive))

				By("Step 8: Verify tenant onboarding was triggered")
				var tenantProgress onboardingModels.OnboardingProgress
				err = db.Where("tenant_id = ? AND type = ?", tenantID, "tenant_setup").First(&tenantProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(tenantProgress.Status).To(Equal("in_progress"))

				By("Step 9: Create website")
				websiteData := map[string]interface{}{
					"name":        "Test Website",
					"domain":      fmt.Sprintf("test-%d.example.com", time.Now().Unix()),
					"description": "Test website for integration testing",
					"settings": map[string]interface{}{
						"theme":    "default",
						"language": "en",
						"timezone": "UTC",
					},
				}

				websiteResponse := makeWebsiteRequest("POST", "/websites", websiteData, accessToken, tenantID)
				Expect(websiteResponse.Code).To(Equal(http.StatusCreated))

				var websiteResult map[string]interface{}
				err = json.Unmarshal(websiteResponse.Body.Bytes(), &websiteResult)
				Expect(err).NotTo(HaveOccurred())
				Expect(websiteResult["success"]).To(BeTrue())

				websiteDataResult := websiteResult["data"].(map[string]interface{})
				websiteID := uint(websiteDataResult["id"].(float64))

				By("Step 10: Verify website creation")
				var createdWebsite websiteModels.Website
				err = db.First(&createdWebsite, websiteID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdWebsite.Name).To(Equal("Test Website"))
				Expect(createdWebsite.TenantID).To(Equal(tenantID))
				Expect(createdWebsite.Status).To(Equal(websiteModels.WebsiteStatusActive))

				By("Step 11: Complete tenant setup onboarding")
				// Get tenant setup journey
				var tenantJourney onboardingModels.OnboardingJourney
				err = db.Where("tenant_id = ? AND type = ?", tenantID, "tenant_setup").First(&tenantJourney).Error
				Expect(err).NotTo(HaveOccurred())

				var tenantSteps []onboardingModels.OnboardingStep
				err = db.Where("journey_id = ?", tenantJourney.ID).Order("order_position ASC").Find(&tenantSteps).Error
				Expect(err).NotTo(HaveOccurred())

				// Complete tenant setup steps
				for _, step := range tenantSteps {
					if step.Type == "action" && step.Name == "Create Your First Website" {
						// This step should be automatically completed since we created a website
						continue
					}

					stepData := generateStepCompletionData(step.Type)
					stepResponse := makeOnboardingRequest("POST", "/onboarding/progress/complete-step", map[string]interface{}{
						"step_id":   step.ID,
						"user_data": stepData,
					}, accessToken)

					Expect(stepResponse.Code).To(Equal(http.StatusOK))
				}

				By("Step 12: Verify complete flow success")
				// Check final user state
				err = db.First(&createdUser, userID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdUser.Status).To(Equal(authModels.UserStatusActive))

				// Check final tenant state
				err = db.First(&createdTenant, tenantID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdTenant.Status).To(Equal(tenantModels.TenantStatusActive))

				// Check final website state
				err = db.First(&createdWebsite, websiteID).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(createdWebsite.Status).To(Equal(websiteModels.WebsiteStatusActive))

				// Check onboarding completion
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("completed"))

				err = db.Where("tenant_id = ? AND type = ?", tenantID, "tenant_setup").First(&tenantProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(tenantProgress.Status).To(Equal("completed"))
			})
		})

		Context("When onboarding is abandoned and resumed", func() {
			It("Should allow resuming from the last completed step", func() {
				By("Step 1: Register user and start onboarding")
				registrationData := map[string]interface{}{
					"email":                 testEmail,
					"password":              testPassword,
					"password_confirmation": testPassword,
					"first_name":            "Jane",
					"last_name":             "Smith",
					"terms_accepted":        true,
				}

				registrationResponse := makeAuthRequest("POST", "/auth/register", registrationData)
				Expect(registrationResponse.Code).To(Equal(http.StatusCreated))

				var regResult map[string]interface{}
				err := json.Unmarshal(registrationResponse.Body.Bytes(), &regResult)
				Expect(err).NotTo(HaveOccurred())

				userData := regResult["data"].(map[string]interface{})
				userID := uint(userData["user"].(map[string]interface{})["id"].(float64))
				accessToken := userData["access_token"].(string)

				By("Step 2: Complete first onboarding step")
				var userProgress onboardingModels.OnboardingProgress
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())

				var firstStep onboardingModels.OnboardingStep
				err = db.Where("journey_id = ? AND order_position = ?", userProgress.JourneyID, 1).First(&firstStep).Error
				Expect(err).NotTo(HaveOccurred())

				stepData := generateStepCompletionData(firstStep.Type)
				stepResponse := makeOnboardingRequest("POST", "/onboarding/progress/complete-step", map[string]interface{}{
					"step_id":   firstStep.ID,
					"user_data": stepData,
				}, accessToken)
				Expect(stepResponse.Code).To(Equal(http.StatusOK))

				By("Step 3: Pause onboarding")
				pauseResponse := makeOnboardingRequest("POST", "/onboarding/progress/pause", map[string]interface{}{
					"journey_id": userProgress.JourneyID,
				}, accessToken)
				Expect(pauseResponse.Code).To(Equal(http.StatusOK))

				// Verify paused state
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("paused"))

				By("Step 4: Resume onboarding")
				resumeResponse := makeOnboardingRequest("POST", "/onboarding/progress/resume", map[string]interface{}{
					"journey_id": userProgress.JourneyID,
				}, accessToken)
				Expect(resumeResponse.Code).To(Equal(http.StatusOK))

				// Verify resumed state
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("in_progress"))
				Expect(userProgress.CurrentStepOrder).To(Equal(uint(2))) // Should be on next step

				By("Step 5: Complete remaining steps")
				var remainingSteps []onboardingModels.OnboardingStep
				err = db.Where("journey_id = ? AND order_position > ?", userProgress.JourneyID, 1).Order("order_position ASC").Find(&remainingSteps).Error
				Expect(err).NotTo(HaveOccurred())

				for _, step := range remainingSteps {
					stepData := generateStepCompletionData(step.Type)
					stepResponse := makeOnboardingRequest("POST", "/onboarding/progress/complete-step", map[string]interface{}{
						"step_id":   step.ID,
						"user_data": stepData,
					}, accessToken)
					Expect(stepResponse.Code).To(Equal(http.StatusOK))
				}

				By("Step 6: Verify completion")
				err = db.Where("user_id = ?", userID).First(&userProgress).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(userProgress.Status).To(Equal("completed"))
			})
		})
	})

	// Helper functions
	setupServices := func() {
		// Initialize repositories
		userRepo := userRepositories.NewUserRepository(db, logger)
		authRepo := authRepositories.NewUserRepository(db, logger)
		sessionRepo := authRepositories.NewSessionRepository(db, logger)
		loginAttemptRepo := authRepositories.NewLoginAttemptRepository(db, logger)
		tenantRepo := tenantRepositories.NewTenantRepository(db, logger)
		websiteRepo := websiteRepositories.NewWebsiteRepository(db, logger)
		onboardingRepo := onboardingRepositories.NewOnboardingRepository(db, logger)

		// Initialize auth services
		jwtService := authServices.NewJWTService(&authServices.JWTConfig{
			SecretKey:       "test-secret-key-32-bytes-long-for-jwt",
			AccessTokenTTL:  time.Hour,
			RefreshTokenTTL: time.Hour * 24 * 7,
			Issuer:          "test-issuer",
		}, nil)

		passwordService := authServices.NewPasswordService(&authServices.PasswordConfig{
			MinLength:        8,
			RequireUppercase: true,
			RequireLowercase: true,
			RequireNumbers:   true,
			RequireSymbols:   false,
		})

		emailService := authServices.NewEmailService(&authServices.EmailConfig{
			SMTPHost:     "localhost",
			SMTPPort:     587,
			SMTPUsername: "<EMAIL>",
			SMTPPassword: "test",
			FromAddress:  "<EMAIL>",
			FromName:     "Test",
		})

		rateLimiter := authServices.NewRateLimitingService(loginAttemptRepo, &authServices.RateLimitConfig{
			MaxAttempts:     5,
			LockoutDuration: time.Minute * 15,
			WindowDuration:  time.Minute * 5,
		})

		authService = authServices.NewAuthService(
			authRepo,
			sessionRepo,
			loginAttemptRepo,
			jwtService,
			passwordService,
			rateLimiter,
			emailService,
			logger,
			&authServices.AuthConfig{
				RequireEmailVerification: false,
				AllowRegistration:        true,
				MaxLoginAttempts:         5,
				LockoutDuration:          time.Minute * 15,
				SessionTimeout:           time.Hour * 24,
				RefreshTokenTTL:          time.Hour * 24 * 7,
				TwoFactorIssuer:          "test-app",
			},
		)

		// Initialize other services
		tenantService = tenantServices.NewTenantService(tenantRepo, logger)
		userService = userServices.NewUserService(userRepo, nil, nil, nil, logger)
		websiteService = websiteServices.NewWebsiteService(websiteRepo, logger)
		onboardingService = onboardingServices.NewOnboardingService(onboardingRepo, nil, logger)
	}

	setupRouter := func() {
		gin.SetMode(gin.TestMode)
		router = gin.New()

		// Initialize validators
		validator := validator.NewValidator()

		// Auth routes
		authHandler := authServices.NewAuthHandler(authService, validator, logger)
		authGroup := router.Group("/auth")
		{
			authGroup.POST("/register", authHandler.Register)
			authGroup.POST("/login", authHandler.Login)
		}

		// Tenant routes
		tenantHandler := tenantHandlers.NewTenantHandler(tenantService, validator, logger)
		tenantGroup := router.Group("/tenants")
		tenantGroup.Use(authMiddleware())
		{
			tenantGroup.POST("", tenantHandler.CreateTenant)
			tenantGroup.GET("/:id", tenantHandler.GetTenant)
		}

		// Website routes
		websiteHandler := websiteHandlers.NewWebsiteHandler(websiteService, validator, logger)
		websiteGroup := router.Group("/websites")
		websiteGroup.Use(authMiddleware())
		websiteGroup.Use(tenantMiddleware())
		{
			websiteGroup.POST("", websiteHandler.CreateWebsite)
			websiteGroup.GET("/:id", websiteHandler.GetWebsite)
		}

		// Onboarding routes
		onboardingHandler := onboardingHandlers.NewOnboardingHandler(onboardingService, validator, logger)
		onboardingGroup := router.Group("/onboarding")
		onboardingGroup.Use(authMiddleware())
		{
			onboardingGroup.POST("/progress/complete-step", onboardingHandler.CompleteStep)
			onboardingGroup.POST("/progress/pause", onboardingHandler.PauseJourney)
			onboardingGroup.POST("/progress/resume", onboardingHandler.ResumeJourney)
			onboardingGroup.GET("/progress", onboardingHandler.GetProgress)
		}
	}

	authMiddleware := func() gin.HandlerFunc {
		return func(c *gin.Context) {
			// Simple auth middleware for testing
			token := c.GetHeader("Authorization")
			if token == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
				c.Abort()
				return
			}

			// Set mock user data for testing
			c.Set("user_id", uint(1))
			c.Set("user_email", testEmail)
			c.Next()
		}
	}

	tenantMiddleware := func() gin.HandlerFunc {
		return func(c *gin.Context) {
			tenantID := c.GetHeader("X-Tenant-ID")
			if tenantID != "" {
				c.Set("tenant_id", tenantID)
			}
			c.Next()
		}
	}

	makeAuthRequest := func(method, path string, data interface{}) *httptest.ResponseRecorder {
		jsonData, _ := json.Marshal(data)
		req, _ := http.NewRequest(method, path, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		return w
	}

	makeTenantRequest := func(method, path string, data interface{}, token string) *httptest.ResponseRecorder {
		jsonData, _ := json.Marshal(data)
		req, _ := http.NewRequest(method, path, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		return w
	}

	makeWebsiteRequest := func(method, path string, data interface{}, token string, tenantID uint) *httptest.ResponseRecorder {
		jsonData, _ := json.Marshal(data)
		req, _ := http.NewRequest(method, path, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", tenantID))

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		return w
	}

	makeOnboardingRequest := func(method, path string, data interface{}, token string) *httptest.ResponseRecorder {
		jsonData, _ := json.Marshal(data)
		req, _ := http.NewRequest(method, path, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		return w
	}

	generateStepCompletionData := func(stepType string) map[string]interface{} {
		switch stepType {
		case "form":
			return map[string]interface{}{
				"first_name": "Test",
				"last_name":  "User",
				"bio":        "This is a test user bio",
			}
		case "preferences":
			return map[string]interface{}{
				"email_notifications": true,
				"theme":               "light",
				"timezone":            "UTC",
			}
		case "tutorial":
			return map[string]interface{}{
				"completed_sections": []string{"navigation", "dashboard", "settings"},
				"time_spent":         300,
			}
		case "action":
			return map[string]interface{}{
				"action_completed": true,
				"completion_time":  time.Now(),
			}
		default:
			return map[string]interface{}{
				"completed": true,
			}
		}
	}
})
