package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	authmodels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant"
	tenantmodels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user"
	usermodels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

var _ = Describe("Auth-Tenant Integration Tests", func() {
	var (
		db         *gorm.DB
		router     *gin.Engine
		logger     utils.Logger
		testServer *httptest.Server
		cleanup    func()

		// Test data
		testTenant1 *tenantmodels.Tenant
		testTenant2 *tenantmodels.Tenant
		testUser1   *usermodels.User
		testUser2   *usermodels.User

		// Auth tokens
		user1Tenant1Token string
		user1Tenant2Token string
		user2Tenant1Token string
		user2Tenant2Token string

		// Helper functions
		makeRequest     func(method, path string, body interface{}, headers map[string]string) *httptest.ResponseRecorder
		registerUser    func(email, password, firstName, lastName string) (*usermodels.User, error)
		loginUser       func(email, password string) (string, error)
		switchTenant    func(token string, tenantID uint) (string, error)
		createTenant    func(name, slug string) (*tenantmodels.Tenant, error)
		addUserToTenant func(userID, tenantID uint, role string) error
	)

	BeforeEach(func() {
		// Setup test database
		var err error

		// Connect to test database
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			utils.GetEnv("TEST_DB_USER", "root"),
			utils.GetEnv("TEST_DB_PASSWORD", "root"),
			utils.GetEnv("TEST_DB_HOST", "localhost"),
			utils.GetEnv("TEST_DB_PORT", "3306"),
			utils.GetEnv("TEST_DB_NAME", "blogapi_test"),
		)

		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
		Expect(err).ToNot(HaveOccurred())

		// Setup logger
		logger = utils.NewLogger("integration-test")

		// Setup validator
		v := validator.New()

		// Setup Gin router
		gin.SetMode(gin.TestMode)
		router = gin.New()

		// Register modules
		apiV1 := router.Group("/api/cms/v1")

		// Register auth routes
		auth.RegisterRoutes(apiV1, db, v, logger)

		// Register tenant routes
		tenant.RegisterRoutes(apiV1, db, v, logger)

		// Register user routes
		user.RegisterRoutes(apiV1, db, v, logger)

		// Start test server
		testServer = httptest.NewServer(router)

		// Setup helper functions
		makeRequest = func(method, path string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
			var reqBody []byte
			if body != nil {
				reqBody, _ = json.Marshal(body)
			}

			req, _ := http.NewRequest(method, path, bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")

			for key, value := range headers {
				req.Header.Set(key, value)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			return w
		}

		registerUser = func(email, password, firstName, lastName string) (*usermodels.User, error) {
			body := map[string]interface{}{
				"email":      email,
				"password":   password,
				"first_name": firstName,
				"last_name":  lastName,
			}

			resp := makeRequest("POST", "/api/cms/v1/auth/register", body, nil)
			if resp.Code != http.StatusCreated {
				return nil, fmt.Errorf("registration failed: %d", resp.Code)
			}

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			if err != nil {
				return nil, err
			}

			userData := result["data"].(map[string]interface{})["user"].(map[string]interface{})
			user := &usermodels.User{
				ID:        uint(userData["id"].(float64)),
				Email:     userData["email"].(string),
				FirstName: userData["first_name"].(string),
				LastName:  userData["last_name"].(string),
			}

			return user, nil
		}

		loginUser = func(email, password string) (string, error) {
			body := map[string]interface{}{
				"email":    email,
				"password": password,
			}

			resp := makeRequest("POST", "/api/cms/v1/auth/login", body, nil)
			if resp.Code != http.StatusOK {
				return "", fmt.Errorf("login failed: %d", resp.Code)
			}

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			if err != nil {
				return "", err
			}

			token := result["data"].(map[string]interface{})["access_token"].(string)
			return token, nil
		}

		switchTenant = func(token string, tenantID uint) (string, error) {
			body := map[string]interface{}{
				"tenant_id": tenantID,
			}

			headers := map[string]string{
				"Authorization": "Bearer " + token,
			}

			resp := makeRequest("POST", "/api/cms/v1/auth/switch-tenant", body, headers)
			if resp.Code != http.StatusOK {
				return "", fmt.Errorf("tenant switch failed: %d", resp.Code)
			}

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			if err != nil {
				return "", err
			}

			newToken := result["data"].(map[string]interface{})["access_token"].(string)
			return newToken, nil
		}

		createTenant = func(name, slug string) (*tenantmodels.Tenant, error) {
			tenant := &tenantmodels.Tenant{
				Name:      name,
				Slug:      slug,
				Status:    "active",
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}

			err := db.Create(tenant).Error
			if err != nil {
				return nil, err
			}

			return tenant, nil
		}

		addUserToTenant = func(userID, tenantID uint, role string) error {
			membership := &usermodels.TenantMembership{
				UserID:    userID,
				TenantID:  tenantID,
				Role:      role,
				Status:    "active",
				IsPrimary: false,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}

			return db.Create(membership).Error
		}

		// Setup cleanup
		cleanup = func() {
			if testServer != nil {
				testServer.Close()
			}

			// Clean up test data
			db.Where("email LIKE ?", "test-%").Delete(&usermodels.User{})
			db.Where("slug LIKE ?", "test-%").Delete(&tenantmodels.Tenant{})
			db.Where("tenant_id IN (SELECT id FROM tenants WHERE slug LIKE ?)", "test-%").Delete(&usermodels.TenantMembership{})
		}

		// Create test data
		var err1, err2 error
		testTenant1, err1 = createTenant("Test Tenant 1", "test-tenant-1")
		testTenant2, err2 = createTenant("Test Tenant 2", "test-tenant-2")
		Expect(err1).ToNot(HaveOccurred())
		Expect(err2).ToNot(HaveOccurred())

		// Create test users
		testUser1, err1 = registerUser("<EMAIL>", "TestPassword123", "Test", "User1")
		testUser2, err2 = registerUser("<EMAIL>", "TestPassword123", "Test", "User2")
		Expect(err1).ToNot(HaveOccurred())
		Expect(err2).ToNot(HaveOccurred())

		// Add users to tenants
		err1 = addUserToTenant(testUser1.ID, testTenant1.ID, "admin")
		err2 = addUserToTenant(testUser1.ID, testTenant2.ID, "member")
		Expect(err1).ToNot(HaveOccurred())
		Expect(err2).ToNot(HaveOccurred())

		err1 = addUserToTenant(testUser2.ID, testTenant1.ID, "member")
		Expect(err1).ToNot(HaveOccurred())

		// Get auth tokens
		user1Tenant1Token, err1 = loginUser("<EMAIL>", "TestPassword123")
		Expect(err1).ToNot(HaveOccurred())

		user1Tenant2Token, err1 = switchTenant(user1Tenant1Token, testTenant2.ID)
		Expect(err1).ToNot(HaveOccurred())

		user2Tenant1Token, err1 = loginUser("<EMAIL>", "TestPassword123")
		Expect(err1).ToNot(HaveOccurred())
	})

	AfterEach(func() {
		cleanup()
	})

	Context("User Authentication with Tenant Context", func() {
		It("should authenticate user with correct tenant context", func() {
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeTrue())
			userData := result["data"].(map[string]interface{})["user"].(map[string]interface{})
			Expect(userData["email"]).To(Equal("<EMAIL>"))

			// Verify tenant context
			if tenantData, exists := result["data"].(map[string]interface{})["tenant"]; exists {
				tenant := tenantData.(map[string]interface{})
				Expect(tenant["id"]).To(Equal(float64(testTenant1.ID)))
				Expect(tenant["name"]).To(Equal("Test Tenant 1"))
			}
		})

		It("should reject requests without proper tenant context", func() {
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				// Missing X-Tenant-ID header
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			// Should still work as profile endpoint doesn't require tenant header
			Expect(resp.Code).To(Equal(http.StatusOK))
		})

		It("should reject requests with invalid tenant context", func() {
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   "99999", // Non-existent tenant
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			// JWT middleware should validate tenant access
			Expect(resp.Code).To(Equal(http.StatusOK)) // Profile endpoint is permissive
		})
	})

	Context("Tenant Isolation", func() {
		It("should enforce tenant isolation for authenticated users", func() {
			// User1 tries to access tenant1 resources with tenant2 token
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant2Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			// Should return tenant2 context, not tenant1
			if tenantData, exists := result["data"].(map[string]interface{})["tenant"]; exists {
				tenant := tenantData.(map[string]interface{})
				Expect(tenant["id"]).To(Equal(float64(testTenant2.ID)))
				Expect(tenant["name"]).To(Equal("Test Tenant 2"))
			}
		})

		It("should prevent cross-tenant access", func() {
			// User2 (only has access to tenant1) tries to access tenant2
			headers := map[string]string{
				"Authorization": "Bearer " + user2Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant2.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			// Should return tenant1 context as user2 doesn't have access to tenant2
			if tenantData, exists := result["data"].(map[string]interface{})["tenant"]; exists {
				tenant := tenantData.(map[string]interface{})
				Expect(tenant["id"]).To(Equal(float64(testTenant1.ID)))
				Expect(tenant["name"]).To(Equal("Test Tenant 1"))
			}
		})
	})

	Context("JWT Token Validation", func() {
		It("should contain correct tenant_id in JWT tokens", func() {
			// Test tenant1 token
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeTrue())
			userData := result["data"].(map[string]interface{})
			Expect(userData["user"]).To(Not(BeNil()))
		})

		It("should validate JWT tokens with tenant context", func() {
			// Test with valid token and matching tenant
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))

			// Test with valid token but different tenant
			headers["X-Tenant-ID"] = strconv.Itoa(int(testTenant2.ID))
			resp = makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			// Should still work but return different tenant context
			Expect(resp.Code).To(Equal(http.StatusOK))
		})

		It("should reject expired or invalid tokens", func() {
			headers := map[string]string{
				"Authorization": "Bearer invalid-token",
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusUnauthorized))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeFalse())
			Expect(result["error"]).To(ContainSubstring("Invalid token"))
		})
	})

	Context("Tenant Switching", func() {
		It("should allow switching between accessible tenants", func() {
			// User1 switches from tenant1 to tenant2
			body := map[string]interface{}{
				"tenant_id": testTenant2.ID,
			}

			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("POST", "/api/cms/v1/auth/switch-tenant", body, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeTrue())
			data := result["data"].(map[string]interface{})
			Expect(data["access_token"]).To(Not(BeEmpty()))

			// Verify new token works with tenant2
			newToken := data["access_token"].(string)
			headers = map[string]string{
				"Authorization": "Bearer " + newToken,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant2.ID)),
			}

			resp = makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))
		})

		It("should prevent switching to inaccessible tenants", func() {
			// User2 tries to switch to tenant2 (no access)
			body := map[string]interface{}{
				"tenant_id": testTenant2.ID,
			}

			headers := map[string]string{
				"Authorization": "Bearer " + user2Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("POST", "/api/cms/v1/auth/switch-tenant", body, headers)

			Expect(resp.Code).To(Equal(http.StatusForbidden))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeFalse())
			Expect(result["error"]).To(ContainSubstring("access"))
		})
	})

	Context("Role-Based Access Control", func() {
		It("should respect tenant-specific roles", func() {
			// User1 is admin in tenant1, member in tenant2

			// Test admin access in tenant1
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))

			// Test member access in tenant2
			headers = map[string]string{
				"Authorization": "Bearer " + user1Tenant2Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant2.ID)),
			}

			resp = makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))
		})

		It("should enforce role-based permissions", func() {
			// User2 is member in tenant1, should have limited access
			headers := map[string]string{
				"Authorization": "Bearer " + user2Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeTrue())
			userData := result["data"].(map[string]interface{})["user"].(map[string]interface{})
			Expect(userData["email"]).To(Equal("<EMAIL>"))
		})
	})

	Context("Session Management", func() {
		It("should track sessions per tenant", func() {
			headers := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/sessions", nil, headers)

			Expect(resp.Code).To(Equal(http.StatusOK))

			var result map[string]interface{}
			err := json.Unmarshal(resp.Body.Bytes(), &result)
			Expect(err).ToNot(HaveOccurred())

			Expect(result["success"]).To(BeTrue())
			sessions := result["data"].(map[string]interface{})["sessions"].([]interface{})
			Expect(len(sessions)).To(BeNumerically(">", 0))
		})

		It("should isolate sessions between tenants", func() {
			// Get sessions for tenant1
			headers1 := map[string]string{
				"Authorization": "Bearer " + user1Tenant1Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp1 := makeRequest("GET", "/api/cms/v1/auth/sessions", nil, headers1)
			Expect(resp1.Code).To(Equal(http.StatusOK))

			// Get sessions for tenant2
			headers2 := map[string]string{
				"Authorization": "Bearer " + user1Tenant2Token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant2.ID)),
			}

			resp2 := makeRequest("GET", "/api/cms/v1/auth/sessions", nil, headers2)
			Expect(resp2.Code).To(Equal(http.StatusOK))

			// Both should return sessions (they're the same user)
			var result1, result2 map[string]interface{}
			json.Unmarshal(resp1.Body.Bytes(), &result1)
			json.Unmarshal(resp2.Body.Bytes(), &result2)

			Expect(result1["success"]).To(BeTrue())
			Expect(result2["success"]).To(BeTrue())
		})
	})

	Context("Performance and Security", func() {
		It("should handle concurrent auth requests", func() {
			const numRequests = 10
			results := make(chan int, numRequests)

			for i := 0; i < numRequests; i++ {
				go func() {
					headers := map[string]string{
						"Authorization": "Bearer " + user1Tenant1Token,
						"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
					}

					resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
					results <- resp.Code
				}()
			}

			// Collect results
			for i := 0; i < numRequests; i++ {
				code := <-results
				Expect(code).To(Equal(http.StatusOK))
			}
		})

		It("should prevent timing attacks", func() {
			start := time.Now()
			headers := map[string]string{
				"Authorization": "Bearer invalid-token",
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			duration1 := time.Since(start)

			start = time.Now()
			headers["Authorization"] = "Bearer " + user1Tenant1Token
			resp = makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			duration2 := time.Since(start)

			Expect(resp.Code).To(Equal(http.StatusOK))

			// Response times should be reasonably consistent
			diff := duration1 - duration2
			if diff < 0 {
				diff = -diff
			}
			Expect(diff).To(BeNumerically("<", 100*time.Millisecond))
		})
	})

	Context("Integration Workflow", func() {
		It("should complete full auth + tenant workflow", func() {
			// 1. Register new user
			newUser, err := registerUser("<EMAIL>", "TestPassword123", "Integration", "Test")
			Expect(err).ToNot(HaveOccurred())

			// 2. Login user
			token, err := loginUser("<EMAIL>", "TestPassword123")
			Expect(err).ToNot(HaveOccurred())

			// 3. Add user to tenant
			err = addUserToTenant(newUser.ID, testTenant1.ID, "member")
			Expect(err).ToNot(HaveOccurred())

			// 4. Access tenant resources
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(testTenant1.ID)),
			}

			resp := makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))

			// 5. Switch tenant (should fail - no access to tenant2)
			switchBody := map[string]interface{}{
				"tenant_id": testTenant2.ID,
			}

			resp = makeRequest("POST", "/api/cms/v1/auth/switch-tenant", switchBody, headers)
			Expect(resp.Code).To(Equal(http.StatusForbidden))

			// 6. Logout
			resp = makeRequest("POST", "/api/cms/v1/auth/logout", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))

			// 7. Verify token is invalid
			resp = makeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusUnauthorized))
		})
	})
})
