package helpers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	tenantmodels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	usermodels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AuthTenantTestHelper provides helper functions for auth-tenant integration tests
type AuthTenantTestHelper struct {
	DB     *gorm.DB
	Router *gin.Engine
	Logger utils.Logger
}

// NewAuthTenantTestHelper creates a new test helper
func NewAuthTenantTestHelper(db *gorm.DB, router *gin.Engine, logger utils.Logger) *AuthTenantTestHelper {
	return &AuthTenantTestHelper{
		DB:     db,
		Router: router,
		Logger: logger,
	}
}

// APIResponse represents a generic API response
type APIResponse struct {
	Success bool                   `json:"success"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   interface{}            `json:"error,omitempty"`
	Meta    map[string]interface{} `json:"meta,omitempty"`
}

// AuthTokens holds authentication tokens
type AuthTokens struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

// TenantContext holds tenant information
type TenantContext struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Slug      string `json:"slug"`
	Status    string `json:"status"`
	IsPrimary bool   `json:"is_primary"`
	Role      string `json:"role"`
}

// UserContext holds user information
type UserContext struct {
	ID            uint            `json:"id"`
	Email         string          `json:"email"`
	FirstName     string          `json:"first_name"`
	LastName      string          `json:"last_name"`
	EmailVerified bool            `json:"email_verified"`
	Status        string          `json:"status"`
	Role          string          `json:"role"`
	Tenants       []TenantContext `json:"tenants,omitempty"`
}

// TestUser represents a test user with tokens
type TestUser struct {
	User   *usermodels.User
	Tokens map[uint]string // tenant_id -> token
}

// TestTenant represents a test tenant
type TestTenant struct {
	Tenant *tenantmodels.Tenant
	Users  []*TestUser
}

// MakeRequest makes an HTTP request to the test server
func (h *AuthTenantTestHelper) MakeRequest(method, path string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}

	req, _ := http.NewRequest(method, path, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	h.Router.ServeHTTP(w, req)
	return w
}

// ParseResponse parses an API response
func (h *AuthTenantTestHelper) ParseResponse(resp *httptest.ResponseRecorder) (*APIResponse, error) {
	var apiResp APIResponse
	err := json.Unmarshal(resp.Body.Bytes(), &apiResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}
	return &apiResp, nil
}

// RegisterUser registers a new user
func (h *AuthTenantTestHelper) RegisterUser(email, password, firstName, lastName string) (*usermodels.User, error) {
	body := map[string]interface{}{
		"email":      email,
		"password":   password,
		"first_name": firstName,
		"last_name":  lastName,
	}

	resp := h.MakeRequest("POST", "/api/cms/v1/auth/register", body, nil)
	if resp.Code != http.StatusCreated {
		return nil, fmt.Errorf("registration failed with status %d: %s", resp.Code, resp.Body.String())
	}

	apiResp, err := h.ParseResponse(resp)
	if err != nil {
		return nil, err
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("registration failed: %v", apiResp.Error)
	}

	userData := apiResp.Data["user"].(map[string]interface{})
	user := &usermodels.User{
		ID:        uint(userData["id"].(float64)),
		Email:     userData["email"].(string),
		FirstName: userData["first_name"].(string),
		LastName:  userData["last_name"].(string),
	}

	return user, nil
}

// LoginUser logs in a user and returns auth tokens
func (h *AuthTenantTestHelper) LoginUser(email, password string) (*AuthTokens, error) {
	body := map[string]interface{}{
		"email":    email,
		"password": password,
	}

	resp := h.MakeRequest("POST", "/api/cms/v1/auth/login", body, nil)
	if resp.Code != http.StatusOK {
		return nil, fmt.Errorf("login failed with status %d: %s", resp.Code, resp.Body.String())
	}

	apiResp, err := h.ParseResponse(resp)
	if err != nil {
		return nil, err
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("login failed: %v", apiResp.Error)
	}

	tokens := &AuthTokens{
		AccessToken:  apiResp.Data["access_token"].(string),
		RefreshToken: apiResp.Data["refresh_token"].(string),
		TokenType:    apiResp.Data["token_type"].(string),
		ExpiresIn:    int(apiResp.Data["expires_in"].(float64)),
	}

	return tokens, nil
}

// SwitchTenant switches to a different tenant
func (h *AuthTenantTestHelper) SwitchTenant(token string, tenantID uint) (*AuthTokens, error) {
	body := map[string]interface{}{
		"tenant_id": tenantID,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := h.MakeRequest("POST", "/api/cms/v1/auth/switch-tenant", body, headers)
	if resp.Code != http.StatusOK {
		return nil, fmt.Errorf("tenant switch failed with status %d: %s", resp.Code, resp.Body.String())
	}

	apiResp, err := h.ParseResponse(resp)
	if err != nil {
		return nil, err
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("tenant switch failed: %v", apiResp.Error)
	}

	tokens := &AuthTokens{
		AccessToken:  apiResp.Data["access_token"].(string),
		RefreshToken: apiResp.Data["refresh_token"].(string),
		TokenType:    apiResp.Data["token_type"].(string),
		ExpiresIn:    int(apiResp.Data["expires_in"].(float64)),
	}

	return tokens, nil
}

// GetUserProfile gets user profile with current context
func (h *AuthTenantTestHelper) GetUserProfile(token string, tenantID uint) (*UserContext, *TenantContext, error) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	if tenantID > 0 {
		headers["X-Tenant-ID"] = strconv.Itoa(int(tenantID))
	}

	resp := h.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
	if resp.Code != http.StatusOK {
		return nil, nil, fmt.Errorf("get profile failed with status %d: %s", resp.Code, resp.Body.String())
	}

	apiResp, err := h.ParseResponse(resp)
	if err != nil {
		return nil, nil, err
	}

	if !apiResp.Success {
		return nil, nil, fmt.Errorf("get profile failed: %v", apiResp.Error)
	}

	// Parse user data
	userData := apiResp.Data["user"].(map[string]interface{})
	userCtx := &UserContext{
		ID:            uint(userData["id"].(float64)),
		Email:         userData["email"].(string),
		FirstName:     userData["first_name"].(string),
		LastName:      userData["last_name"].(string),
		EmailVerified: userData["email_verified"].(bool),
		Status:        userData["status"].(string),
	}

	// Parse tenant data if present
	var tenantCtx *TenantContext
	if tenantData, exists := apiResp.Data["tenant"]; exists && tenantData != nil {
		tenant := tenantData.(map[string]interface{})
		tenantCtx = &TenantContext{
			ID:     uint(tenant["id"].(float64)),
			Name:   tenant["name"].(string),
			Slug:   tenant["slug"].(string),
			Status: tenant["status"].(string),
		}
	}

	return userCtx, tenantCtx, nil
}

// CreateTenant creates a test tenant
func (h *AuthTenantTestHelper) CreateTenant(name, slug string) (*tenantmodels.Tenant, error) {
	tenant := &tenantmodels.Tenant{
		Name:      name,
		Slug:      slug,
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := h.DB.Create(tenant).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	return tenant, nil
}

// AddUserToTenant adds a user to a tenant with a specific role
func (h *AuthTenantTestHelper) AddUserToTenant(userID, tenantID uint, role string, isPrimary bool) error {
	membership := &usermodels.TenantMembership{
		UserID:    userID,
		TenantID:  tenantID,
		Role:      role,
		Status:    "active",
		IsPrimary: isPrimary,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := h.DB.Create(membership).Error
	if err != nil {
		return fmt.Errorf("failed to add user to tenant: %w", err)
	}

	return nil
}

// RemoveUserFromTenant removes a user from a tenant
func (h *AuthTenantTestHelper) RemoveUserFromTenant(userID, tenantID uint) error {
	err := h.DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).Delete(&usermodels.TenantMembership{}).Error
	if err != nil {
		return fmt.Errorf("failed to remove user from tenant: %w", err)
	}

	return nil
}

// GetUserTenants gets all tenants for a user
func (h *AuthTenantTestHelper) GetUserTenants(userID uint) ([]TenantContext, error) {
	var memberships []usermodels.TenantMembership
	err := h.DB.Preload("Tenant").Where("user_id = ? AND status = ?", userID, "active").Find(&memberships).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user tenants: %w", err)
	}

	var tenants []TenantContext
	for _, membership := range memberships {
		tenant := TenantContext{
			ID:        membership.Tenant.ID,
			Name:      membership.Tenant.Name,
			Slug:      membership.Tenant.Slug,
			Status:    membership.Tenant.Status,
			IsPrimary: membership.IsPrimary,
			Role:      membership.Role,
		}
		tenants = append(tenants, tenant)
	}

	return tenants, nil
}

// CleanupTestData cleans up test data
func (h *AuthTenantTestHelper) CleanupTestData() error {
	// Delete test users
	err := h.DB.Where("email LIKE ?", "test-%").Delete(&usermodels.User{}).Error
	if err != nil {
		return fmt.Errorf("failed to cleanup test users: %w", err)
	}

	// Delete test tenants
	err = h.DB.Where("slug LIKE ?", "test-%").Delete(&tenantmodels.Tenant{}).Error
	if err != nil {
		return fmt.Errorf("failed to cleanup test tenants: %w", err)
	}

	// Delete test memberships
	err = h.DB.Where("tenant_id IN (SELECT id FROM tenants WHERE slug LIKE ?)", "test-%").Delete(&usermodels.TenantMembership{}).Error
	if err != nil {
		return fmt.Errorf("failed to cleanup test memberships: %w", err)
	}

	return nil
}

// AssertUserExists checks if a user exists in the database
func (h *AuthTenantTestHelper) AssertUserExists(userID uint) error {
	var user usermodels.User
	err := h.DB.First(&user, userID).Error
	if err != nil {
		return fmt.Errorf("user %d does not exist: %w", userID, err)
	}
	return nil
}

// AssertUserHasStatus checks if a user has a specific status
func (h *AuthTenantTestHelper) AssertUserHasStatus(userID uint, status string) error {
	var user usermodels.User
	err := h.DB.First(&user, userID).Error
	if err != nil {
		return fmt.Errorf("user %d does not exist: %w", userID, err)
	}

	if user.Status != status {
		return fmt.Errorf("user %d has status %s, expected %s", userID, user.Status, status)
	}

	return nil
}

// AssertTenantExists checks if a tenant exists in the database
func (h *AuthTenantTestHelper) AssertTenantExists(tenantID uint) error {
	var tenant tenantmodels.Tenant
	err := h.DB.First(&tenant, tenantID).Error
	if err != nil {
		return fmt.Errorf("tenant %d does not exist: %w", tenantID, err)
	}
	return nil
}

// AssertTenantHasStatus checks if a tenant has a specific status
func (h *AuthTenantTestHelper) AssertTenantHasStatus(tenantID uint, status string) error {
	var tenant tenantmodels.Tenant
	err := h.DB.First(&tenant, tenantID).Error
	if err != nil {
		return fmt.Errorf("tenant %d does not exist: %w", tenantID, err)
	}

	if tenant.Status != status {
		return fmt.Errorf("tenant %d has status %s, expected %s", tenantID, tenant.Status, status)
	}

	return nil
}

// AssertUserHasTenantAccess checks if a user has access to a tenant
func (h *AuthTenantTestHelper) AssertUserHasTenantAccess(userID, tenantID uint) error {
	var membership usermodels.TenantMembership
	err := h.DB.Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, "active").First(&membership).Error
	if err != nil {
		return fmt.Errorf("user %d does not have access to tenant %d: %w", userID, tenantID, err)
	}
	return nil
}

// AssertUserHasRole checks if a user has a specific role in a tenant
func (h *AuthTenantTestHelper) AssertUserHasRole(userID, tenantID uint, role string) error {
	var membership usermodels.TenantMembership
	err := h.DB.Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, "active").First(&membership).Error
	if err != nil {
		return fmt.Errorf("user %d does not have membership in tenant %d: %w", userID, tenantID, err)
	}

	if membership.Role != role {
		return fmt.Errorf("user %d has role %s in tenant %d, expected %s", userID, membership.Role, tenantID, role)
	}

	return nil
}

// TestScenarioData holds data for complex test scenarios
type TestScenarioData struct {
	Tenants map[string]*tenantmodels.Tenant
	Users   map[string]*usermodels.User
	Tokens  map[string]map[string]string // user -> tenant -> token
}

// CreateTestScenario creates a complex test scenario with multiple users and tenants
func (h *AuthTenantTestHelper) CreateTestScenario() (*TestScenarioData, error) {
	data := &TestScenarioData{
		Tenants: make(map[string]*tenantmodels.Tenant),
		Users:   make(map[string]*usermodels.User),
		Tokens:  make(map[string]map[string]string),
	}

	// Create test tenants
	tenant1, err := h.CreateTenant("Test Corp", "test-corp")
	if err != nil {
		return nil, err
	}
	data.Tenants["corp"] = tenant1

	tenant2, err := h.CreateTenant("Test Startup", "test-startup")
	if err != nil {
		return nil, err
	}
	data.Tenants["startup"] = tenant2

	// Create test users
	admin, err := h.RegisterUser("<EMAIL>", "AdminPass123", "Admin", "User")
	if err != nil {
		return nil, err
	}
	data.Users["admin"] = admin

	member, err := h.RegisterUser("<EMAIL>", "MemberPass123", "Member", "User")
	if err != nil {
		return nil, err
	}
	data.Users["member"] = member

	guest, err := h.RegisterUser("<EMAIL>", "GuestPass123", "Guest", "User")
	if err != nil {
		return nil, err
	}
	data.Users["guest"] = guest

	// Setup tenant memberships
	err = h.AddUserToTenant(admin.ID, tenant1.ID, "admin", true)
	if err != nil {
		return nil, err
	}

	err = h.AddUserToTenant(admin.ID, tenant2.ID, "admin", false)
	if err != nil {
		return nil, err
	}

	err = h.AddUserToTenant(member.ID, tenant1.ID, "member", true)
	if err != nil {
		return nil, err
	}

	err = h.AddUserToTenant(guest.ID, tenant1.ID, "viewer", true)
	if err != nil {
		return nil, err
	}

	// Generate tokens
	data.Tokens["admin"] = make(map[string]string)
	data.Tokens["member"] = make(map[string]string)
	data.Tokens["guest"] = make(map[string]string)

	// Admin tokens
	adminTokens, err := h.LoginUser("<EMAIL>", "AdminPass123")
	if err != nil {
		return nil, err
	}
	data.Tokens["admin"]["corp"] = adminTokens.AccessToken

	adminTokens2, err := h.SwitchTenant(adminTokens.AccessToken, tenant2.ID)
	if err != nil {
		return nil, err
	}
	data.Tokens["admin"]["startup"] = adminTokens2.AccessToken

	// Member tokens
	memberTokens, err := h.LoginUser("<EMAIL>", "MemberPass123")
	if err != nil {
		return nil, err
	}
	data.Tokens["member"]["corp"] = memberTokens.AccessToken

	// Guest tokens
	guestTokens, err := h.LoginUser("<EMAIL>", "GuestPass123")
	if err != nil {
		return nil, err
	}
	data.Tokens["guest"]["corp"] = guestTokens.AccessToken

	return data, nil
}
