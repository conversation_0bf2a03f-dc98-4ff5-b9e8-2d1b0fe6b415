package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostSEOHandler handles SEO operations for blog posts
type BlogPostSEOHandler struct {
	seoMetaService services.SEOMetaService
	logger         utils.Logger
}

// NewBlogPostSEOHandler creates a new blog post SEO handler
func NewBlogPostSEOHandler(seoMetaService services.SEOMetaService, logger utils.Logger) *BlogPostSEOHandler {
	return &BlogPostSEOHandler{
		seoMetaService: seoMetaService,
		logger:         logger,
	}
}

// CreatePostSEO creates SEO metadata for a blog post
// @Summary Create SEO metadata for blog post
// @Description Create SEO metadata for a specific blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.CreateSEOMetaRequest true "Create SEO meta request"
// @Success 201 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 422 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo [post]
func (h *BlogPostSEOHandler) CreatePostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req models.CreateSEOMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Set page-specific fields for blog post
	postIDUint := uint(postID)
	req.PageType = models.PageTypePost
	req.PageID = &postIDUint

	// If page_url not provided, generate default
	if req.PageURL == "" {
		req.PageURL = "/blog/posts/" + c.Param("id")
	}

	// If page_path not provided, generate default
	if req.PagePath == "" {
		req.PagePath = "/blog/posts/" + c.Param("id")
	}

	seoMeta, err := h.seoMetaService.CreateMeta(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to create SEO metadata", err.Error())
		return
	}

	response.Created(c.Writer, seoMeta)
}

// GetPostSEO retrieves SEO metadata for a blog post
// @Summary Get SEO metadata for blog post
// @Description Get SEO metadata for a specific blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo [get]
func (h *BlogPostSEOHandler) GetPostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	seoMeta, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to get SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	response.Success(c.Writer, seoMeta)
}

// UpdatePostSEO updates SEO metadata for a blog post
// @Summary Update SEO metadata for blog post
// @Description Update SEO metadata for a specific blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.UpdateSEOMetaRequest true "Update SEO meta request"
// @Success 200 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 422 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo [put]
func (h *BlogPostSEOHandler) UpdatePostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req models.UpdateSEOMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	// First get the existing SEO metadata to get the SEO meta ID
	existing, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to find SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	seoMeta, err := h.seoMetaService.UpdateMeta(c.Request.Context(), existing.ID, &req, tenantID)
	if err != nil {
		h.logger.Error("Failed to update SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to update SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, seoMeta)
}

// DeletePostSEO deletes SEO metadata for a blog post
// @Summary Delete SEO metadata for blog post
// @Description Delete SEO metadata for a specific blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo [delete]
func (h *BlogPostSEOHandler) DeletePostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	// First get the existing SEO metadata to get the SEO meta ID
	existing, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to find SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	err = h.seoMetaService.DeleteMeta(c.Request.Context(), existing.ID, tenantID)
	if err != nil {
		h.logger.Error("Failed to delete SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to delete SEO metadata", err.Error())
		return
	}

	response.NoContent(c.Writer)
}

// AnalyzePostSEO analyzes SEO metadata for a blog post
// @Summary Analyze SEO metadata for blog post
// @Description Analyze SEO metadata and provide optimization recommendations for blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response{data=models.SEOAnalysis}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo/analyze [post]
func (h *BlogPostSEOHandler) AnalyzePostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	// First get the existing SEO metadata to get the SEO meta ID
	existing, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to find SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	analysis, err := h.seoMetaService.AnalyzeMeta(c.Request.Context(), existing.ID, tenantID)
	if err != nil {
		h.logger.Error("Failed to analyze SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to analyze SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, analysis)
}

// ValidatePostSEO validates SEO metadata for a blog post
// @Summary Validate SEO metadata for blog post
// @Description Validate SEO metadata and check for issues for blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response{data=models.SEOValidationResult}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo/validate [post]
func (h *BlogPostSEOHandler) ValidatePostSEO(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	// First get the existing SEO metadata to get the SEO meta ID
	existing, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to find SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	result, err := h.seoMetaService.ValidateMeta(c.Request.Context(), existing.ID, tenantID)
	if err != nil {
		h.logger.Error("Failed to validate SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to validate SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GeneratePostMetaTags generates HTML meta tags for a blog post
// @Summary Generate meta tags for blog post
// @Description Generate HTML meta tags for SEO metadata for blog post
// @Tags Blog SEO
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/seo/tags [get]
func (h *BlogPostSEOHandler) GeneratePostMetaTags(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	postIDUint := uint(postID)

	// First get the existing SEO metadata to get the SEO meta ID
	existing, err := h.seoMetaService.GetMetaByPage(
		c.Request.Context(),
		models.PageTypePost,
		&postIDUint,
		"",
		tenantID,
	)
	if err != nil {
		h.logger.Error("Failed to find SEO meta for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found for blog post")
		return
	}

	tags, structuredData, err := h.seoMetaService.GenerateMetaTags(c.Request.Context(), existing.ID, tenantID)
	if err != nil {
		h.logger.Error("Failed to generate meta tags for post", "error", err, "post_id", postID, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to generate meta tags", err.Error())
		return
	}

	result := map[string]interface{}{
		"meta_tags":       tags,
		"structured_data": structuredData,
		"post_id":         postID,
	}

	response.Success(c.Writer, result)
}
