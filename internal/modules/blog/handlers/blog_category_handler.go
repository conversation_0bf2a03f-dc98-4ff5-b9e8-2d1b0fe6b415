package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type BlogCategoryHandler struct {
	categoryService services.BlogCategoryService
}

func NewBlogCategoryHandler(categoryService services.BlogCategoryService) *BlogCategoryHandler {
	return &BlogCategoryHandler{
		categoryService: categoryService,
	}
}

// CreateCategory creates a new blog category
func (h *BlogCategoryHandler) CreateCategory(c *gin.Context) {
	var req models.BlogCategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	req.TenantID = tenantID.(uint)

	category, err := h.categoryService.Create(c.Request.Context(), &req)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to create category")
		return
	}

	httpresponse.Created(c.Writer, category)
}

// GetCategory retrieves a blog category by ID
func (h *BlogCategoryHandler) GetCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid category ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.GetByID(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Category not found")
		return
	}

	httpresponse.OK(c.Writer, category)
}

// GetCategoryBySlug retrieves a blog category by slug
func (h *BlogCategoryHandler) GetCategoryBySlug(c *gin.Context) {
	slug := c.Param("slug")
	websiteIDStr := c.Query("website_id")

	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.GetBySlug(c.Request.Context(), tenantID.(uint), uint(websiteID), slug)
	if err != nil {
		httpresponse.NotFound(c.Writer, "Category not found")
		return
	}

	httpresponse.OK(c.Writer, category)
}

// UpdateCategory updates a blog category
func (h *BlogCategoryHandler) UpdateCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid category ID")
		return
	}

	var req models.BlogCategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	category, err := h.categoryService.Update(c.Request.Context(), tenantID.(uint), uint(id), &req)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to update category")
		return
	}

	httpresponse.OK(c.Writer, category)
}

// DeleteCategory deletes a blog category
func (h *BlogCategoryHandler) DeleteCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid category ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.categoryService.Delete(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to delete category")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// ListCategories lists blog categories with filtering and supports both page and cursor pagination
func (h *BlogCategoryHandler) ListCategories(c *gin.Context) {
	var filter models.BlogCategoryFilter

	// Parse basic query parameters
	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		if websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
			filter.WebsiteID = uint(websiteID)
		}
	}

	if parentIDStr := c.Query("parent_id"); parentIDStr != "" {
		if parentID, err := strconv.ParseUint(parentIDStr, 10, 32); err == nil {
			parentIDUint := uint(parentID)
			filter.ParentID = &parentIDUint
		}
	}

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	filter.Status = c.Query("status")
	filter.Search = c.Query("search")
	filter.SortBy = c.DefaultQuery("sort_by", "name")
	filter.SortOrder = c.DefaultQuery("sort_order", "asc")

	// Set tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	filter.TenantID = tenantID.(uint)

	// Initialize pagination manager with default config
	migrationConfig := pagination.DefaultMigrationConfig()
	paginationManager := pagination.NewPaginationManager(migrationConfig)

	// Handle pagination parameters - supports both page and cursor
	flexReq, paginationType, cursorReq := paginationManager.HandleRequest(c)

	// Set pagination defaults based on detected type
	if paginationType == pagination.PagePagination {
		// Convert page parameters to filter for traditional pagination
		if flexReq.Page != nil && flexReq.PageSize != nil {
			filter.Page = *flexReq.Page
			filter.PageSize = *flexReq.PageSize

			// Use traditional pagination service
			categories, total, err := h.categoryService.List(c.Request.Context(), &filter)
			if err != nil {
				httpresponse.InternalServerError(c.Writer, "Failed to list categories")
				return
			}

			// Build page-based response
			data := map[string]interface{}{
				"categories": categories,
				"meta": map[string]interface{}{
					"page":        filter.Page,
					"page_size":   filter.PageSize,
					"total":       int(total),
					"total_pages": (int(total) + filter.PageSize - 1) / filter.PageSize,
					"has_next":    filter.Page < (int(total)+filter.PageSize-1)/filter.PageSize,
					"has_prev":    filter.Page > 1,
				},
			}

			httpresponse.OK(c.Writer, data)
			return
		}
	}

	// Use cursor-based pagination (default)
	filters := map[string]interface{}{
		"website_id": filter.WebsiteID,
		"status":     filter.Status,
		"search":     filter.Search,
		"is_active":  filter.IsActive,
		"parent_id":  filter.ParentID,
		"sort_by":    filter.SortBy,
		"sort_order": filter.SortOrder,
	}

	response, err := h.categoryService.ListWithCursor(c.Request.Context(), filter.TenantID, cursorReq, filters)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to list categories with cursor")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// GetCategoryHierarchy retrieves the category hierarchy
func (h *BlogCategoryHandler) GetCategoryHierarchy(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	categories, err := h.categoryService.GetHierarchy(c.Request.Context(), tenantID.(uint), uint(websiteID))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get category hierarchy")
		return
	}

	httpresponse.OK(c.Writer, categories)
}

// MoveCategory moves a category to a new parent
func (h *BlogCategoryHandler) MoveCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid category ID")
		return
	}

	var req struct {
		NewParentID uint `json:"new_parent_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.categoryService.MoveCategory(c.Request.Context(), tenantID.(uint), uint(id), req.NewParentID)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to move category")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// UpdateCategoryPositions updates category positions for reordering
func (h *BlogCategoryHandler) UpdateCategoryPositions(c *gin.Context) {
	var req struct {
		Positions []services.CategoryPosition `json:"positions"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err := h.categoryService.UpdatePositions(c.Request.Context(), tenantID.(uint), req.Positions)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to update category positions")
		return
	}

	httpresponse.OK(c.Writer, nil)
}
