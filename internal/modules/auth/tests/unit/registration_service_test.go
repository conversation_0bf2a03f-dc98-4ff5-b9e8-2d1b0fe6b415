package unit

import (
	"context"
	"errors"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/mocks"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

var _ = Describe("Registration Service", func() {
	var (
		authService      services.AuthService
		mockUserRepo     *mocks.MockUserRepository
		mockSessionRepo  *mocks.MockSessionRepository
		mockLoginRepo    *mocks.MockLoginAttemptRepository
		mockJWTService   *mocks.MockJWTService
		mockPasswordSvc  *mocks.MockPasswordService
		mockRateLimiter  *mocks.MockRateLimitingService
		mockEmailService *mocks.MockEmailService
		mockLogger       *mocks.MockLogger
		config           *services.AuthConfig
		ctx              context.Context
	)

	BeforeEach(func() {
		// Initialize all mocks
		mockUserRepo = new(mocks.MockUserRepository)
		mockSessionRepo = new(mocks.MockSessionRepository)
		mockLoginRepo = new(mocks.MockLoginAttemptRepository)
		mockJWTService = new(mocks.MockJWTService)
		mockPasswordSvc = new(mocks.MockPasswordService)
		mockRateLimiter = new(mocks.MockRateLimitingService)
		mockEmailService = new(mocks.MockEmailService)
		mockLogger = new(mocks.MockLogger)

		// Test configuration
		config = &services.AuthConfig{
			RequireEmailVerification: false,
			AllowRegistration:        true,
			MaxLoginAttempts:         5,
			LockoutDuration:          time.Minute * 15,
			SessionTimeout:           time.Hour * 24,
			RefreshTokenTTL:          time.Hour * 24 * 7,
			TwoFactorIssuer:          "test-app",
		}

		// Create service
		authService = services.NewAuthService(
			mockUserRepo,
			mockSessionRepo,
			mockLoginRepo,
			mockJWTService,
			mockPasswordSvc,
			mockRateLimiter,
			mockEmailService,
			mockLogger,
			config,
		)

		ctx = context.Background()
	})

	AfterEach(func() {
		// Verify all expectations were met
		mockUserRepo.AssertExpectations(GinkgoT())
		mockSessionRepo.AssertExpectations(GinkgoT())
		mockLoginRepo.AssertExpectations(GinkgoT())
		mockJWTService.AssertExpectations(GinkgoT())
		mockPasswordSvc.AssertExpectations(GinkgoT())
		mockRateLimiter.AssertExpectations(GinkgoT())
		mockEmailService.AssertExpectations(GinkgoT())
		mockLogger.AssertExpectations(GinkgoT())
	})

	Describe("Register", func() {
		Context("with valid registration data", func() {
			It("should register user successfully", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID:  1,
					WebsiteID: 1,
					Email:     "<EMAIL>",
					Username:  "testuser",
					Password:  "SecurePass123",
					FirstName: "Test",
					LastName:  "User",
					IPAddress: "127.0.0.1",
					UserAgent: "Mozilla/5.0 Test Browser",
				}

				// Mock password validation
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)

				// Mock email existence check
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)

				// Mock username existence check
				mockUserRepo.On("ExistsByUsername", mock.Anything, request.Username).Return(false, nil)

				// Mock password hashing
				hashedPassword := "$2a$10$hashed.password.example"
				mockPasswordSvc.On("HashPassword", request.Password).Return(hashedPassword, nil)

				// Mock user creation
				mockUserRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.User")).
					Return(nil).
					Run(func(args mock.Arguments) {
						user := args.Get(1).(*models.User)
						user.ID = 1
						user.CreatedAt = time.Now()
						user.UpdatedAt = time.Now()
					})

				// Mock login flow (auto-login after registration)
				mockRateLimiter.On("IsAccountLocked", mock.Anything, request.Email).Return(false, time.Duration(0), nil)
				mockUserRepo.On("GetByEmailOrUsername", mock.Anything, request.Email).Return(&models.User{
					ID:            1,
					TenantID:      1,
					Email:         request.Email,
					PasswordHash:  hashedPassword,
					Status:        models.UserStatusActive,
					Role:          models.UserRoleUser,
					EmailVerified: true,
				}, nil)

				// Mock password verification
				mockPasswordSvc.On("VerifyPassword", hashedPassword, request.Password).Return(nil)

				// Mock session creation
				mockSessionRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.Session")).
					Return(nil).
					Run(func(args mock.Arguments) {
						session := args.Get(1).(*models.Session)
						session.ID = 1
						session.CreatedAt = time.Now()
					})

				// Mock JWT token generation
				mockJWTService.On("GenerateTokenPair", mock.AnythingOfType("*models.JWTClaims")).
					Return("access_token", "refresh_token", nil)

				// Mock session update with refresh token
				mockSessionRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.Session")).Return(nil)

				// Mock last login update
				mockUserRepo.On("UpdateLastLogin", mock.Anything, uint(1), request.IPAddress).Return(nil)

				// Mock successful login recording
				mockRateLimiter.On("RecordSuccessfulLogin", mock.Anything, uint(1), request.IPAddress).Return(nil)

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).NotTo(HaveOccurred())
				Expect(response).NotTo(BeNil())
				Expect(response.User).NotTo(BeNil())
				Expect(response.User.ID).To(Equal(uint(1)))
				Expect(response.User.Email).To(Equal(request.Email))
				Expect(response.User.Status).To(Equal(models.UserStatusActive))
				Expect(response.AccessToken).To(Equal("access_token"))
				Expect(response.RefreshToken).To(Equal("refresh_token"))
				Expect(response.TokenType).To(Equal("Bearer"))
				Expect(response.SessionID).To(Equal(uint(1)))
			})

			It("should register user with minimal data", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID:  1,
					WebsiteID: 1,
					Email:     "<EMAIL>",
					Password:  "SecurePass123",
					IPAddress: "127.0.0.1",
					UserAgent: "Mozilla/5.0 Test Browser",
				}

				// Setup similar mocks as above but without username checks
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)
				mockPasswordSvc.On("HashPassword", request.Password).Return("$2a$10$hashed", nil)

				mockUserRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.User")).
					Return(nil).
					Run(func(args mock.Arguments) {
						user := args.Get(1).(*models.User)
						user.ID = 2
						// Verify that optional fields are nil
						Expect(user.Username).To(BeNil())
						Expect(user.FirstName).To(BeNil())
						Expect(user.LastName).To(BeNil())
					})

				// Mock login flow
				mockRateLimiter.On("IsAccountLocked", mock.Anything, request.Email).Return(false, time.Duration(0), nil)
				mockUserRepo.On("GetByEmailOrUsername", mock.Anything, request.Email).Return(&models.User{
					ID:            2,
					TenantID:      1,
					Email:         request.Email,
					PasswordHash:  "$2a$10$hashed",
					Status:        models.UserStatusActive,
					Role:          models.UserRoleUser,
					EmailVerified: true,
				}, nil)

				mockPasswordSvc.On("VerifyPassword", "$2a$10$hashed", request.Password).Return(nil)
				mockSessionRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.Session")).
					Return(nil).
					Run(func(args mock.Arguments) {
						session := args.Get(1).(*models.Session)
						session.ID = 2
					})

				mockJWTService.On("GenerateTokenPair", mock.AnythingOfType("*models.JWTClaims")).
					Return("access_token_2", "refresh_token_2", nil)

				mockSessionRepo.On("Update", mock.Anything, mock.AnythingOfType("*models.Session")).Return(nil)
				mockUserRepo.On("UpdateLastLogin", mock.Anything, uint(2), request.IPAddress).Return(nil)
				mockRateLimiter.On("RecordSuccessfulLogin", mock.Anything, uint(2), request.IPAddress).Return(nil)

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).NotTo(HaveOccurred())
				Expect(response).NotTo(BeNil())
				Expect(response.User.ID).To(Equal(uint(2)))
				Expect(response.User.Email).To(Equal(request.Email))
			})
		})

		Context("with registration disabled", func() {
			BeforeEach(func() {
				config.AllowRegistration = false
			})

			It("should return error when registration is disabled", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("registration is disabled"))
				Expect(response).To(BeNil())
			})
		})

		Context("with weak password", func() {
			It("should return weak password error", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "weak",
				}

				// Mock password validation failure
				mockPasswordSvc.On("ValidatePassword", request.Password).
					Return(errors.New("password too weak"))

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err).To(Equal(services.ErrWeakPassword))
				Expect(response).To(BeNil())
			})
		})

		Context("with existing email", func() {
			It("should return email already exists error", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Mock password validation
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)

				// Mock email existence check
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(true, nil)

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err).To(Equal(services.ErrEmailAlreadyExists))
				Expect(response).To(BeNil())
			})
		})

		Context("with existing username", func() {
			It("should return username already exists error", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Username: "existinguser",
					Password: "SecurePass123",
				}

				// Mock password validation
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)

				// Mock email existence check
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)

				// Mock username existence check
				mockUserRepo.On("ExistsByUsername", mock.Anything, request.Username).Return(true, nil)

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err).To(Equal(services.ErrUsernameAlreadyExists))
				Expect(response).To(BeNil())
			})
		})

		Context("with database errors", func() {
			It("should handle user creation failure", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Mock successful validations
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)
				mockPasswordSvc.On("HashPassword", request.Password).Return("$2a$10$hashed", nil)

				// Mock user creation failure
				mockUserRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.User")).
					Return(errors.New("database error"))

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("failed to create user"))
				Expect(response).To(BeNil())
			})

			It("should handle email existence check failure", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Mock password validation
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)

				// Mock email existence check failure
				mockUserRepo.On("Exists", mock.Anything, request.Email).
					Return(false, errors.New("database connection error"))

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("failed to check email existence"))
				Expect(response).To(BeNil())
			})

			It("should handle password hashing failure", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Mock successful validations
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)

				// Mock password hashing failure
				mockPasswordSvc.On("HashPassword", request.Password).
					Return("", errors.New("hashing error"))

				// Act
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("failed to hash password"))
				Expect(response).To(BeNil())
			})
		})

		Context("with email verification enabled", func() {
			BeforeEach(func() {
				config.RequireEmailVerification = true
			})

			It("should create user with email verification required", func() {
				// Arrange
				request := &services.RegisterRequest{
					TenantID: 1,
					Email:    "<EMAIL>",
					Password: "SecurePass123",
				}

				// Mock successful validations
				mockPasswordSvc.On("ValidatePassword", request.Password).Return(nil)
				mockUserRepo.On("Exists", mock.Anything, request.Email).Return(false, nil)
				mockPasswordSvc.On("HashPassword", request.Password).Return("$2a$10$hashed", nil)

				// Mock user creation - verify email verification is false
				mockUserRepo.On("Create", mock.Anything, mock.AnythingOfType("*models.User")).
					Return(nil).
					Run(func(args mock.Arguments) {
						user := args.Get(1).(*models.User)
						user.ID = 1
						// Verify email verification is required
						Expect(user.EmailVerified).To(BeFalse())
						Expect(user.EmailVerifiedAt).To(BeNil())
					})

				// Mock email service
				mockEmailService.On("SendVerificationEmail", request.Email, mock.AnythingOfType("string")).
					Return(nil)

				// Mock login flow for auto-login
				mockRateLimiter.On("IsAccountLocked", mock.Anything, request.Email).Return(false, time.Duration(0), nil)
				mockUserRepo.On("GetByEmailOrUsername", mock.Anything, request.Email).Return(&models.User{
					ID:            1,
					TenantID:      1,
					Email:         request.Email,
					PasswordHash:  "$2a$10$hashed",
					Status:        models.UserStatusActive,
					Role:          models.UserRoleUser,
					EmailVerified: false, // Not verified yet
				}, nil)

				// Since email verification is required and user is not verified,
				// the login should fail with email not verified error
				response, err := authService.Register(ctx, request)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err).To(Equal(services.ErrEmailNotVerified))
				Expect(response).To(BeNil())
			})
		})
	})

	Describe("VerifyEmail", func() {
		It("should return not implemented error", func() {
			// Act
			err := authService.VerifyEmail(ctx, "some-token")

			// Assert
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("email verification not implemented"))
		})
	})

	Describe("ResendVerificationEmail", func() {
		Context("with valid user", func() {
			It("should resend verification email", func() {
				// Arrange
				email := "<EMAIL>"
				user := &models.User{
					ID:            1,
					Email:         email,
					EmailVerified: false,
				}

				// Mock user lookup
				mockUserRepo.On("GetByEmail", mock.Anything, email).Return(user, nil)

				// Mock email service
				mockEmailService.On("SendVerificationEmail", email, mock.AnythingOfType("string")).
					Return(nil)

				// Act
				err := authService.ResendVerificationEmail(ctx, email)

				// Assert
				Expect(err).NotTo(HaveOccurred())
			})

			It("should return error if email already verified", func() {
				// Arrange
				email := "<EMAIL>"
				now := time.Now()
				user := &models.User{
					ID:              1,
					Email:           email,
					EmailVerified:   true,
					EmailVerifiedAt: &now,
				}

				// Mock user lookup
				mockUserRepo.On("GetByEmail", mock.Anything, email).Return(user, nil)

				// Act
				err := authService.ResendVerificationEmail(ctx, email)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("email already verified"))
			})
		})

		Context("with invalid user", func() {
			It("should return user not found error", func() {
				// Arrange
				email := "<EMAIL>"

				// Mock user lookup failure
				mockUserRepo.On("GetByEmail", mock.Anything, email).Return(nil, gorm.ErrRecordNotFound)

				// Act
				err := authService.ResendVerificationEmail(ctx, email)

				// Assert
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("failed to get user"))
			})
		})
	})
})
