package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// SEORedirectCreateRequest represents request to create SEO redirect
type SEORedirectCreateRequest struct {
	WebsiteID uint `json:"website_id" validate:"required" example:"1"`

	// Redirect Information
	SourceURL       string  `json:"source_url" validate:"required,max=500" example:"/old-page"`
	SourcePath      string  `json:"source_path" validate:"required,max=500" example:"/old-page"`
	DestinationURL  string  `json:"destination_url" validate:"required,max=500" example:"https://example.com/new-page"`
	DestinationPath *string `json:"destination_path" validate:"omitempty,max=500" example:"/new-page"`

	// Redirect Configuration
	RedirectType       models.RedirectType  `json:"redirect_type" validate:"omitempty,oneof=301 302 303 307 308" example:"301"`
	RedirectMatch      models.RedirectMatch `json:"redirect_match" validate:"omitempty,oneof=exact regex wildcard" example:"exact"`
	RegexPattern       *string              `json:"regex_pattern" validate:"omitempty,max=1000" example:"^/old-section/(.*)$"`
	ReplacementPattern *string              `json:"replacement_pattern" validate:"omitempty,max=1000" example:"/new-section/$1"`

	// Conditional Redirects
	Conditions          map[string]interface{}     `json:"conditions" example:"{\"user_agent\":\"bot\",\"referrer\":\"google.com\"}"`
	QueryStringHandling models.QueryStringHandling `json:"query_string_handling" validate:"omitempty,oneof=ignore preserve append" example:"preserve"`

	// SEO Information
	SEOReason *string `json:"seo_reason" validate:"omitempty,max=100" example:"page_moved"`
	Notes     *string `json:"notes" validate:"omitempty,max=1000" example:"Redirecting old product page to new structure"`

	// Scheduling
	ActiveFrom  *time.Time `json:"active_from" example:"2024-01-01T00:00:00Z"`
	ActiveUntil *time.Time `json:"active_until" example:"2024-12-31T23:59:59Z"`

	// Status
	Status models.RedirectStatus `json:"status" validate:"omitempty,oneof=active inactive expired deleted" example:"active"`
}

// SEORedirectUpdateRequest represents request to update SEO redirect
type SEORedirectUpdateRequest struct {
	// Redirect Information
	SourceURL       *string `json:"source_url" validate:"omitempty,max=500" example:"/updated-old-page"`
	SourcePath      *string `json:"source_path" validate:"omitempty,max=500" example:"/updated-old-page"`
	DestinationURL  *string `json:"destination_url" validate:"omitempty,max=500" example:"https://example.com/updated-new-page"`
	DestinationPath *string `json:"destination_path" validate:"omitempty,max=500" example:"/updated-new-page"`

	// Redirect Configuration
	RedirectType       *models.RedirectType  `json:"redirect_type" validate:"omitempty,oneof=301 302 303 307 308" example:"302"`
	RedirectMatch      *models.RedirectMatch `json:"redirect_match" validate:"omitempty,oneof=exact regex wildcard" example:"regex"`
	RegexPattern       *string               `json:"regex_pattern" validate:"omitempty,max=1000" example:"^/old-(.*)$"`
	ReplacementPattern *string               `json:"replacement_pattern" validate:"omitempty,max=1000" example:"/new-$1"`

	// Conditional Redirects
	Conditions          map[string]interface{}      `json:"conditions"`
	QueryStringHandling *models.QueryStringHandling `json:"query_string_handling" validate:"omitempty,oneof=ignore preserve append" example:"append"`

	// SEO Information
	SEOReason *string `json:"seo_reason" validate:"omitempty,max=100" example:"seo_optimization"`
	Notes     *string `json:"notes" validate:"omitempty,max=1000" example:"Updated redirect for better SEO"`

	// Scheduling
	ActiveFrom  *time.Time `json:"active_from" example:"2024-02-01T00:00:00Z"`
	ActiveUntil *time.Time `json:"active_until" example:"2024-11-30T23:59:59Z"`

	// Status
	Status *models.RedirectStatus `json:"status" validate:"omitempty,oneof=active inactive expired deleted" example:"active"`
}

// SEORedirectResponse represents SEO redirect information in responses
type SEORedirectResponse struct {
	ID        uint `json:"id" example:"1"`
	WebsiteID uint `json:"website_id" example:"1"`
	TenantID  uint `json:"tenant_id" example:"1"`

	// Redirect Information
	SourceURL       string  `json:"source_url" example:"/old-page"`
	SourcePath      string  `json:"source_path" example:"/old-page"`
	DestinationURL  string  `json:"destination_url" example:"https://example.com/new-page"`
	DestinationPath *string `json:"destination_path,omitempty" example:"/new-page"`

	// Redirect Configuration
	RedirectType       models.RedirectType  `json:"redirect_type" example:"301"`
	RedirectMatch      models.RedirectMatch `json:"redirect_match" example:"exact"`
	RegexPattern       *string              `json:"regex_pattern,omitempty" example:"^/old-(.*)$"`
	ReplacementPattern *string              `json:"replacement_pattern,omitempty" example:"/new-$1"`

	// Conditional Redirects
	Conditions          map[string]interface{}     `json:"conditions"`
	QueryStringHandling models.QueryStringHandling `json:"query_string_handling" example:"preserve"`

	// Tracking and Analytics
	HitCount         uint       `json:"hit_count" example:"150"`
	LastHitAt        *time.Time `json:"last_hit_at,omitempty"`
	LastHitIP        *string    `json:"last_hit_ip,omitempty" example:"*************"`
	LastHitUserAgent *string    `json:"last_hit_user_agent,omitempty" example:"Mozilla/5.0..."`
	LastHitReferrer  *string    `json:"last_hit_referrer,omitempty" example:"https://google.com"`

	// SEO Information
	SEOReason *string `json:"seo_reason,omitempty" example:"page_moved"`
	Notes     *string `json:"notes,omitempty" example:"Redirecting old product page"`
	CreatedBy *uint   `json:"created_by,omitempty" example:"123"`
	UpdatedBy *uint   `json:"updated_by,omitempty" example:"124"`

	// Scheduling
	ActiveFrom  *time.Time `json:"active_from,omitempty"`
	ActiveUntil *time.Time `json:"active_until,omitempty"`

	// Status and Timestamps
	Status    models.RedirectStatus `json:"status" example:"active"`
	CreatedAt time.Time             `json:"created_at"`
	UpdatedAt time.Time             `json:"updated_at"`
}

// SEORedirectListResponse represents response for listing SEO redirects
type SEORedirectListResponse struct {
	Redirects  []SEORedirectResponse      `json:"redirects"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	// Legacy fields for backward compatibility
	Total      int64 `json:"total,omitempty" example:"50"`
	Page       int   `json:"page,omitempty" example:"1"`
	PageSize   int   `json:"page_size,omitempty" example:"20"`
	TotalPages int   `json:"total_pages,omitempty" example:"3"`
}

// SEORedirectFilter represents filter parameters for listing redirects
type SEORedirectFilter struct {
	pagination.CursorRequest
	WebsiteID    *uint                  `json:"website_id" form:"website_id" example:"1"`
	Status       *models.RedirectStatus `json:"status,omitempty" form:"status" validate:"omitempty,oneof=active inactive expired deleted" example:"active"`
	RedirectType *models.RedirectType   `json:"redirect_type,omitempty" form:"redirect_type" validate:"omitempty,oneof=301 302 303 307 308" example:"301"`
	SourceURL    *string                `json:"source_url,omitempty" form:"source_url" example:"/old-page"`
	Search       string                 `json:"search,omitempty" form:"search" example:"product"`
	CreatedBy    *uint                  `json:"created_by,omitempty" form:"created_by" example:"123"`
	DateFrom     *time.Time             `json:"date_from,omitempty" form:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo       *time.Time             `json:"date_to,omitempty" form:"date_to" example:"2024-12-31T23:59:59Z"`
	SortBy       string                 `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id created_at updated_at hit_count last_hit_at" example:"hit_count"`
	SortOrder    string                 `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
	// Legacy fields for backward compatibility
	Page     int `json:"page,omitempty" form:"page" validate:"min=0" example:"1"`
	PageSize int `json:"page_size,omitempty" form:"page_size" validate:"min=0,max=100" example:"20"`
}

// SEORedirectStatsResponse represents redirect statistics
type SEORedirectStatsResponse struct {
	TotalRedirects     int                   `json:"total_redirects" example:"150"`
	ActiveRedirects    int                   `json:"active_redirects" example:"120"`
	InactiveRedirects  int                   `json:"inactive_redirects" example:"20"`
	ExpiredRedirects   int                   `json:"expired_redirects" example:"10"`
	TotalHits          int                   `json:"total_hits" example:"5000"`
	Top301Redirects    int                   `json:"top_301_redirects" example:"100"`
	Top302Redirects    int                   `json:"top_302_redirects" example:"30"`
	RecentHits         int                   `json:"recent_hits" example:"250"`
	AvgHitsPerRedirect float64               `json:"avg_hits_per_redirect" example:"33.3"`
	TopRedirects       []SEORedirectHitStats `json:"top_redirects"`
}

// SEORedirectHitStats represents hit statistics for individual redirects
type SEORedirectHitStats struct {
	RedirectID     uint                `json:"redirect_id" example:"1"`
	SourceURL      string              `json:"source_url" example:"/old-page"`
	DestinationURL string              `json:"destination_url" example:"/new-page"`
	HitCount       uint                `json:"hit_count" example:"150"`
	RedirectType   models.RedirectType `json:"redirect_type" example:"301"`
	LastHitAt      *time.Time          `json:"last_hit_at,omitempty"`
}

// SEORedirectBulkActionRequest represents request for bulk operations on redirects
type SEORedirectBulkActionRequest struct {
	RedirectIDs []uint `json:"redirect_ids" validate:"required,min=1" example:"1,2,3"`
	Action      string `json:"action" validate:"required,oneof=activate deactivate delete expire" example:"activate"`
	Reason      string `json:"reason,omitempty" example:"Bulk activation for SEO campaign"`
}

// SEORedirectBulkActionResponse represents response for bulk operations
type SEORedirectBulkActionResponse struct {
	Success      bool     `json:"success" example:"true"`
	Message      string   `json:"message" example:"Bulk action completed successfully"`
	ProcessedIDs []uint   `json:"processed_ids" example:"1,2,3"`
	FailedIDs    []uint   `json:"failed_ids,omitempty"`
	SkippedIDs   []uint   `json:"skipped_ids,omitempty"`
	Errors       []string `json:"errors,omitempty"`
}

// SEORedirectValidationRequest represents request for redirect validation
type SEORedirectValidationRequest struct {
	SourceURL      string               `json:"source_url" validate:"required" example:"/old-page"`
	DestinationURL string               `json:"destination_url" validate:"required" example:"/new-page"`
	RedirectType   models.RedirectType  `json:"redirect_type" validate:"required,oneof=301 302 303 307 308" example:"301"`
	RedirectMatch  models.RedirectMatch `json:"redirect_match" validate:"required,oneof=exact regex wildcard" example:"exact"`
	RegexPattern   *string              `json:"regex_pattern" example:"^/old-(.*)$"`
	CheckConflicts bool                 `json:"check_conflicts" example:"true"`
	CheckChains    bool                 `json:"check_chains" example:"true"`
	CheckLoops     bool                 `json:"check_loops" example:"true"`
}

// SEORedirectValidationResponse represents response for redirect validation
type SEORedirectValidationResponse struct {
	IsValid              bool                          `json:"is_valid" example:"true"`
	Errors               []models.ValidationError      `json:"errors,omitempty"`
	Warnings             []models.ValidationWarning    `json:"warnings,omitempty"`
	Suggestions          []models.ValidationSuggestion `json:"suggestions,omitempty"`
	ConflictingRedirects []SEORedirectConflict         `json:"conflicting_redirects,omitempty"`
	RedirectChain        []SEORedirectChainStep        `json:"redirect_chain,omitempty"`
	HasLoop              bool                          `json:"has_loop" example:"false"`
	ChainLength          int                           `json:"chain_length" example:"1"`
}

// SEORedirectConflict represents a conflicting redirect
type SEORedirectConflict struct {
	RedirectID     uint                  `json:"redirect_id" example:"2"`
	SourceURL      string                `json:"source_url" example:"/old-page"`
	DestinationURL string                `json:"destination_url" example:"/different-page"`
	RedirectType   models.RedirectType   `json:"redirect_type" example:"302"`
	Status         models.RedirectStatus `json:"status" example:"active"`
	ConflictType   string                `json:"conflict_type" example:"duplicate_source"`
}

// SEORedirectChainStep represents a step in redirect chain
type SEORedirectChainStep struct {
	Step           int                 `json:"step" example:"1"`
	SourceURL      string              `json:"source_url" example:"/old-page"`
	DestinationURL string              `json:"destination_url" example:"/intermediate-page"`
	RedirectType   models.RedirectType `json:"redirect_type" example:"301"`
	RedirectID     uint                `json:"redirect_id" example:"1"`
}

// SEORedirectImportRequest represents request for importing redirects
type SEORedirectImportRequest struct {
	Format            string                `json:"format" validate:"required,oneof=csv json apache_htaccess nginx" example:"csv"`
	Data              string                `json:"data" validate:"required" example:"source,destination,type\n/old1,/new1,301\n/old2,/new2,302"`
	OverwriteExisting bool                  `json:"overwrite_existing" example:"false"`
	SkipValidation    bool                  `json:"skip_validation" example:"false"`
	DefaultType       models.RedirectType   `json:"default_type" validate:"omitempty,oneof=301 302 303 307 308" example:"301"`
	DefaultStatus     models.RedirectStatus `json:"default_status" validate:"omitempty,oneof=active inactive" example:"active"`
}

// SEORedirectImportResponse represents response for redirect import
type SEORedirectImportResponse struct {
	Success           bool                  `json:"success" example:"true"`
	Message           string                `json:"message" example:"Redirects imported successfully"`
	ImportedCount     int                   `json:"imported_count" example:"25"`
	SkippedCount      int                   `json:"skipped_count" example:"2"`
	ErrorCount        int                   `json:"error_count" example:"1"`
	Errors            []string              `json:"errors,omitempty"`
	SkippedItems      []string              `json:"skipped_items,omitempty"`
	ImportedRedirects []SEORedirectResponse `json:"imported_redirects,omitempty"`
}

// SEORedirectExportRequest represents request for exporting redirects
type SEORedirectExportRequest struct {
	Format       string                 `json:"format" validate:"required,oneof=csv json apache_htaccess nginx" example:"csv"`
	RedirectIDs  []uint                 `json:"redirect_ids" example:"1,2,3"`
	Status       *models.RedirectStatus `json:"status" validate:"omitempty,oneof=active inactive expired" example:"active"`
	RedirectType *models.RedirectType   `json:"redirect_type" validate:"omitempty,oneof=301 302 303 307 308" example:"301"`
	IncludeStats bool                   `json:"include_stats" example:"true"`
}

// SEORedirectExportResponse represents response for redirect export
type SEORedirectExportResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Redirects exported successfully"`
	Format      string    `json:"format" example:"csv"`
	Data        string    `json:"data" example:"source,destination,type,hits\n/old1,/new1,301,150"`
	DownloadURL string    `json:"download_url" example:"https://api.example.com/exports/redirects_123.csv"`
	ExportID    string    `json:"export_id" example:"export_abc123"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// SEORedirectTestRequest represents request for testing redirect
type SEORedirectTestRequest struct {
	SourceURL          string               `json:"source_url" validate:"required" example:"/test-page"`
	RedirectType       models.RedirectType  `json:"redirect_type" validate:"required,oneof=301 302 303 307 308" example:"301"`
	RedirectMatch      models.RedirectMatch `json:"redirect_match" validate:"required,oneof=exact regex wildcard" example:"exact"`
	RegexPattern       *string              `json:"regex_pattern" example:"^/test-(.*)$"`
	ReplacementPattern *string              `json:"replacement_pattern" example:"/new-$1"`
	QueryString        string               `json:"query_string" example:"param1=value1&param2=value2"`
	UserAgent          string               `json:"user_agent" example:"Mozilla/5.0..."`
	Referrer           string               `json:"referrer" example:"https://google.com"`
}

// SEORedirectTestResponse represents response for redirect testing
type SEORedirectTestResponse struct {
	Success            bool                `json:"success" example:"true"`
	Matched            bool                `json:"matched" example:"true"`
	SourceURL          string              `json:"source_url" example:"/test-page"`
	DestinationURL     string              `json:"destination_url" example:"/new-page"`
	RedirectType       models.RedirectType `json:"redirect_type" example:"301"`
	FinalURL           string              `json:"final_url" example:"/new-page?param1=value1&param2=value2"`
	MatchedPattern     string              `json:"matched_pattern,omitempty" example:"^/test-(.*)$"`
	AppliedReplacement string              `json:"applied_replacement,omitempty" example:"/new-$1"`
	QueryHandling      string              `json:"query_handling" example:"preserved"`
	TestResult         string              `json:"test_result" example:"Redirect would work correctly"`
}

// ToServiceModel converts SEORedirectCreateRequest to models.SEORedirectCreateRequest
func (r *SEORedirectCreateRequest) ToServiceModel() models.SEORedirectCreateRequest {
	return models.SEORedirectCreateRequest{
		WebsiteID:           r.WebsiteID,
		SourceURL:           r.SourceURL,
		SourcePath:          r.SourcePath,
		DestinationURL:      r.DestinationURL,
		DestinationPath:     r.DestinationPath,
		RedirectType:        r.RedirectType,
		RedirectMatch:       r.RedirectMatch,
		RegexPattern:        r.RegexPattern,
		ReplacementPattern:  r.ReplacementPattern,
		Conditions:          r.Conditions,
		QueryStringHandling: r.QueryStringHandling,
		SEOReason:           r.SEOReason,
		Notes:               r.Notes,
		ActiveFrom:          r.ActiveFrom,
		ActiveUntil:         r.ActiveUntil,
		Status:              r.Status,
	}
}

// ToServiceModel converts SEORedirectUpdateRequest to models.SEORedirectUpdateRequest
func (r *SEORedirectUpdateRequest) ToServiceModel() models.SEORedirectUpdateRequest {
	return models.SEORedirectUpdateRequest{
		SourceURL:           r.SourceURL,
		SourcePath:          r.SourcePath,
		DestinationURL:      r.DestinationURL,
		DestinationPath:     r.DestinationPath,
		RedirectType:        r.RedirectType,
		RedirectMatch:       r.RedirectMatch,
		RegexPattern:        r.RegexPattern,
		ReplacementPattern:  r.ReplacementPattern,
		Conditions:          r.Conditions,
		QueryStringHandling: r.QueryStringHandling,
		SEOReason:           r.SEOReason,
		Notes:               r.Notes,
		ActiveFrom:          r.ActiveFrom,
		ActiveUntil:         r.ActiveUntil,
		Status:              r.Status,
	}
}
