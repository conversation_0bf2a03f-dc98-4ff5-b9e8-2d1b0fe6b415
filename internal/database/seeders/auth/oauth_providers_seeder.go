package auth

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// OAuthProvider represents an OAuth provider configuration
type OAuthProvider struct {
	ID               uint   `gorm:"primaryKey"`
	WebsiteID        uint   `gorm:"not null;index"`
	Name             string `gorm:"not null"`
	DisplayName      string `gorm:"not null"`
	ProviderType     string `gorm:"not null;default:'oauth2'"`
	ClientID         string `gorm:"not null"`
	ClientSecret     string `gorm:"not null"`
	Scopes           string `gorm:"type:json"`
	AuthURL          string `gorm:"column:auth_url;not null"`
	TokenURL         string `gorm:"column:token_url;not null"`
	UserInfoURL      string `gorm:"column:user_info_url"`
	IsEnabled        bool   `gorm:"default:true"`
	IsDefault        bool   `gorm:"default:false"`
	AutoCreateUsers  bool   `gorm:"default:true"`
	UserMapping      string `gorm:"type:json"`
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

// TableName sets the table name for OAuthProvider
func (OAuthProvider) TableName() string {
	return "auth_oauth_providers"
}

// OAuthProvidersSeeder handles seeding of OAuth providers
type OAuthProvidersSeeder struct{}

// Seed creates sample OAuth providers for development and testing
func (s *OAuthProvidersSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert map to JSON string
	toJSON := func(m interface{}) string {
		b, _ := json.Marshal(m)
		return string(b)
	}

	// Check if we have any websites to associate providers with
	var websiteCount int64
	if err := db.Table("websites").Count(&websiteCount).Error; err != nil {
		return err
	}

	if websiteCount == 0 {
		// No websites exist, skip seeding OAuth providers
		return nil
	}

	// Get the first website ID
	var websiteID uint
	if err := db.Table("websites").Select("id").Order("id ASC").Limit(1).Scan(&websiteID).Error; err != nil {
		return err
	}

	providers := []OAuthProvider{
		{
			WebsiteID:       websiteID,
			Name:            "google",
			DisplayName:     "Google",
			ProviderType:    "oauth2",
			ClientID:        "your-google-client-id.apps.googleusercontent.com",
			ClientSecret:    "your-google-client-secret",
			Scopes:          toJSON([]string{"email", "profile", "openid"}),
			AuthURL:         "https://accounts.google.com/o/oauth2/v2/auth",
			TokenURL:        "https://oauth2.googleapis.com/token",
			UserInfoURL:     "https://www.googleapis.com/oauth2/v2/userinfo",
			IsEnabled:       true,
			IsDefault:       false,
			AutoCreateUsers: true,
			UserMapping: toJSON(map[string]string{
				"id":             "id",
				"email":          "email",
				"email_verified": "verified_email",
				"name":           "name",
				"first_name":     "given_name",
				"last_name":      "family_name",
				"avatar_url":     "picture",
				"locale":         "locale",
			}),
		},
		{
			WebsiteID:       websiteID,
			Name:            "facebook",
			DisplayName:     "Facebook",
			ProviderType:    "oauth2",
			ClientID:        "your-facebook-app-id",
			ClientSecret:    "your-facebook-app-secret",
			Scopes:          toJSON([]string{"email", "public_profile"}),
			AuthURL:         "https://www.facebook.com/v19.0/dialog/oauth",
			TokenURL:        "https://graph.facebook.com/v19.0/oauth/access_token",
			UserInfoURL:     "https://graph.facebook.com/me?fields=id,email,name,first_name,last_name,picture",
			IsEnabled:       true,
			IsDefault:       false,
			AutoCreateUsers: true,
			UserMapping: toJSON(map[string]string{
				"id":         "id",
				"email":      "email",
				"name":       "name",
				"first_name": "first_name",
				"last_name":  "last_name",
				"avatar_url": "picture.data.url",
			}),
		},
		{
			WebsiteID:       websiteID,
			Name:            "github",
			DisplayName:     "GitHub",
			ProviderType:    "oauth2",
			ClientID:        "your-github-client-id",
			ClientSecret:    "your-github-client-secret",
			Scopes:          toJSON([]string{"user:email", "read:user"}),
			AuthURL:         "https://github.com/login/oauth/authorize",
			TokenURL:        "https://github.com/login/oauth/access_token",
			UserInfoURL:     "https://api.github.com/user",
			IsEnabled:       false,
			IsDefault:       false,
			AutoCreateUsers: true,
			UserMapping: toJSON(map[string]string{
				"id":         "id",
				"email":      "email",
				"name":       "name",
				"first_name": "name",
				"last_name":  "",
				"avatar_url": "avatar_url",
				"username":   "login",
			}),
		},
	}

	for _, provider := range providers {
		var existing OAuthProvider
		if err := db.Where("website_id = ? AND name = ?", provider.WebsiteID, provider.Name).First(&existing).Error; err == nil {
			// Update existing provider
			db.Model(&existing).Updates(provider)
		} else {
			// Create new provider
			if err := db.Create(&provider).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// Rollback removes seeded OAuth providers
func (s *OAuthProvidersSeeder) Rollback(db *gorm.DB) error {
	names := []string{"google", "facebook", "github"}
	return db.Where("name IN ?", names).Delete(&OAuthProvider{}).Error
}

// Name returns the name of this seeder
func (s *OAuthProvidersSeeder) Name() string {
	return "oauth_providers"
}
