package seeders

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/blog"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/notification"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/onboarding"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/rbac"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/tenant"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/user"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/website"
)

// Manager handles database seeding operations
type Manager struct {
	db            *sql.DB
	seeders       map[string]Seeder
	legacySeeders map[string]LegacySeeder
	order         []string
}

// NewManager creates a new seeder manager
func NewManager(db *sql.DB) *Manager {
	manager := &Manager{
		db:            db,
		seeders:       make(map[string]Seeder),
		legacySeeders: make(map[string]LegacySeeder),
		order:         []string{},
	}

	// Register all seeders here
	manager.registerSeeders()

	return manager
}

// registerSeeders registers all available seeders
func (m *Manager) registerSeeders() {
	// Tenant module seeders (should run first since other modules depend on tenants)
	tenantSeeder := tenant.NewTenantModuleSeeder(m.db)
	m.register(tenantSeeder)

	// Website module seeders (should run before user seeder since users depend on websites)
	websiteSeeder := website.NewWebsiteModuleSeeder(m.db)
	m.register(websiteSeeder)

	// Auth module seeders (should run after website seeder since OAuth providers depend on websites)
	authSeeder := auth.NewAuthModuleSeeder(m.db)
	m.register(authSeeder)

	// User module seeders
	userSeeder := user.NewUserSeeder(m.db)
	m.register(userSeeder)

	// RBAC module seeders
	rbacSeeder := rbac.NewRBACSeeder(m.db)
	m.register(rbacSeeder)

	// Blog module seeders
	blogSeeder := blog.NewBlogSeeder(m.db)
	m.register(blogSeeder)

	// Onboarding module seeders
	onboardingSeeder := onboarding.NewOnboardingModuleSeeder(m.db)
	m.register(onboardingSeeder)

	// Notification module seeders
	notificationSeeder := notification.NewNotificationSeeder(m.db)
	m.register(notificationSeeder)
}

// register adds a seeder to the manager
func (m *Manager) register(seeder Seeder) {
	name := seeder.Name()
	if _, exists := m.seeders[name]; exists {
		log.Printf("Warning: Seeder %s already registered, skipping", name)
		return
	}
	m.seeders[name] = seeder
	m.order = append(m.order, name)
}

// registerLegacy adds a legacy seeder to the manager
func (m *Manager) registerLegacy(seeder LegacySeeder) {
	name := seeder.Name()
	if _, exists := m.legacySeeders[name]; exists {
		log.Printf("Warning: Legacy seeder %s already registered, skipping", name)
		return
	}
	m.legacySeeders[name] = seeder
	m.order = append(m.order, name)
}

// RunAll executes all registered seeders
func (m *Manager) RunAll() error {
	log.Println("Starting database seeding...")

	ctx := context.Background()
	for _, name := range m.order {
		// Check if it's a regular seeder
		if seeder, exists := m.seeders[name]; exists {
			log.Printf("Running seeder: %s", name)
			if err := seeder.Seed(ctx); err != nil {
				return fmt.Errorf("failed to run seeder %s: %w", name, err)
			}
			log.Printf("✅ Seeder %s completed successfully", name)
		} else if legacySeeder, exists := m.legacySeeders[name]; exists {
			log.Printf("Running legacy seeder: %s", name)
			if err := legacySeeder.Seed(m.db); err != nil {
				return fmt.Errorf("failed to run legacy seeder %s: %w", name, err)
			}
			log.Printf("✅ Legacy seeder %s completed successfully", name)
		} else {
			return fmt.Errorf("seeder %s not found", name)
		}
	}

	log.Println("✅ All seeders completed successfully")
	return nil
}

// RunSpecific runs specific seeders
func (m *Manager) RunSpecific(names []string) error {
	log.Println("Running specific seeders...")

	ctx := context.Background()
	for _, name := range names {
		// Check if it's a regular seeder
		if seeder, exists := m.seeders[name]; exists {
			log.Printf("Running seeder: %s", name)
			if err := seeder.Seed(ctx); err != nil {
				return fmt.Errorf("failed to run seeder %s: %w", name, err)
			}
			log.Printf("✅ Seeder %s completed successfully", name)
		} else if legacySeeder, exists := m.legacySeeders[name]; exists {
			log.Printf("Running legacy seeder: %s", name)
			if err := legacySeeder.Seed(m.db); err != nil {
				return fmt.Errorf("failed to run legacy seeder %s: %w", name, err)
			}
			log.Printf("✅ Legacy seeder %s completed successfully", name)
		} else {
			return fmt.Errorf("seeder not found: %s", name)
		}
	}

	return nil
}

// List returns a list of all registered seeders
func (m *Manager) List() []string {
	return m.order
}

// RollbackAll rolls back all seeders in reverse order
func (m *Manager) RollbackAll() error {
	log.Println("Rolling back all seeders...")

	// Roll back seeders in reverse order
	for i := len(m.order) - 1; i >= 0; i-- {
		name := m.order[i]

		// Check if it's a legacy seeder that supports rollback
		if legacySeeder, exists := m.legacySeeders[name]; exists {
			log.Printf("Rolling back legacy seeder: %s", name)
			if err := legacySeeder.Rollback(m.db); err != nil {
				return fmt.Errorf("failed to rollback legacy seeder %s: %w", name, err)
			}
			log.Printf("✅ Legacy seeder %s rolled back successfully", name)
		} else {
			log.Printf("⚠️ Seeder %s does not support rollback", name)
		}
	}

	log.Println("✅ All seeders rolled back successfully")
	return nil
}
