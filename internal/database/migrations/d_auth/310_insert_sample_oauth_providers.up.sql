-- Migration: 310_insert_sample_oauth_providers
-- Description: Insert sample OAuth providers (Google and Facebook) for development and testing
-- Author: System
-- Date: 2025-01-23

-- Create sample tenant if it doesn't exist (for development/testing)
INSERT IGNORE INTO tenants (
    id,
    name,
    slug,
    domain,
    status,
    owner_email,
    billing_email,
    support_email,
    settings,
    metadata,
    created_at,
    updated_at
) VALUES (
    1,
    'Sample Tenant',
    'sample-tenant',
    'sample',
    'active',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '{"timezone": "UTC", "language": "en"}',
    '{"industry": "Technology", "companySize": "1-10"}',
    NOW(),
    NOW()
);

-- Create sample website if it doesn't exist (for development/testing)
INSERT IGNORE INTO websites (
    id,
    tenant_id,
    name,
    subdomain,
    description,
    active_theme,
    timezone,
    language,
    status,
    created_at,
    updated_at
) VALUES (
    1,
    1,
    'Sample Website',
    'sample-site',
    'Sample website for OAuth provider testing',
    'default',
    'UTC',
    'en',
    'active',
    NOW(),
    NOW()
);

-- Insert Google OAuth Provider
INSERT INTO auth_oauth_providers (
    website_id,
    name,
    display_name,
    provider_type,
    client_id,
    client_secret,
    scopes,
    auth_url,
    token_url,
    user_info_url,
    is_enabled,
    is_default,
    auto_create_users,
    user_mapping
) VALUES (
    1,
    'google',
    'Google',
    'oauth2',
    'your-google-client-id.apps.googleusercontent.com',
    'your-google-client-secret',
    '["email", "profile", "openid"]',
    'https://accounts.google.com/o/oauth2/v2/auth',
    'https://oauth2.googleapis.com/token',
    'https://www.googleapis.com/oauth2/v2/userinfo',
    1,
    0,
    1,
    '{"id": "id", "email": "email", "name": "name", "first_name": "given_name", "last_name": "family_name", "avatar_url": "picture"}'
);

-- Insert Facebook OAuth Provider
INSERT INTO auth_oauth_providers (
    website_id,
    name,
    display_name,
    provider_type,
    client_id,
    client_secret,
    scopes,
    auth_url,
    token_url,
    user_info_url,
    is_enabled,
    is_default,
    auto_create_users,
    user_mapping
) VALUES (
    1,
    'facebook',
    'Facebook',
    'oauth2',
    'your-facebook-app-id',
    'your-facebook-app-secret',
    '["email", "public_profile"]',
    'https://www.facebook.com/v19.0/dialog/oauth',
    'https://graph.facebook.com/v19.0/oauth/access_token',
    'https://graph.facebook.com/me?fields=id,email,name,first_name,last_name,picture',
    1,
    0,
    1,
    '{"id": "id", "email": "email", "name": "name", "first_name": "first_name", "last_name": "last_name", "avatar_url": "picture.data.url"}'
);

-- Note: Remember to update client_id and client_secret with actual values from:
-- Google: https://console.developers.google.com/
-- Facebook: https://developers.facebook.com/apps/