-- Migration: 311_add_redirect_urls_to_oauth_providers
-- Description: Add redirect_urls support and fix schema inconsistency for multi-domain OAuth
-- Author: System
-- Date: 2025-01-23

-- Add redirect_urls and redirect_url columns
ALTER TABLE auth_oauth_providers
ADD COLUMN redirect_urls JSON DEFAULT ('[]'),
ADD COLUMN redirect_url VARCHAR(500);

-- Note: JSON columns don't support direct indexing in MySQL 8.0
-- If indexing is needed later, use generated columns on specific JSON paths

-- Update existing providers with redirect URLs (only if they exist)
-- This is safe to run even if no providers exist
UPDATE auth_oauth_providers
SET
    redirect_url = CASE
        WHEN name = 'google' THEN 'https://localhost:3000/auth/oauth/google/callback'
        WHEN name = 'facebook' THEN 'https://localhost:3000/auth/oauth/facebook/callback'
        ELSE 'https://localhost:3000/auth/oauth/callback'
    END,
    redirect_urls = CASE
        WHEN name = 'google' THEN '["https://localhost:3000/auth/oauth/google/callback", "https://app.example.com/auth/oauth/google/callback"]'
        WHEN name = 'facebook' THEN '["https://localhost:3000/auth/oauth/facebook/callback", "https://app.example.com/auth/oauth/facebook/callback"]'
        ELSE '["https://localhost:3000/auth/oauth/callback"]'
    END
WHERE EXISTS (SELECT 1 FROM websites WHERE id = 1)
  AND website_id = 1;