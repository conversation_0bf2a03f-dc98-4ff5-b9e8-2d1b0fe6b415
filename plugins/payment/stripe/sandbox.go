package stripe

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/stripe/stripe-go/v76"
	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// SandboxConfig holds sandbox-specific configuration
type SandboxConfig struct {
	// Sandbox mode settings
	Enabled     bool   `json:"enabled"`
	APIBase     string `json:"api_base"`
	ConnectBase string `json:"connect_base"`
	UploadsBase string `json:"uploads_base"`

	// Test data settings
	AutoGenerateTestData bool          `json:"auto_generate_test_data"`
	TestDataSeed         int64         `json:"test_data_seed"`
	SimulateLatency      bool          `json:"simulate_latency"`
	LatencyRange         time.Duration `json:"latency_range"`

	// Test scenarios
	DefaultTestMode string            `json:"default_test_mode"`
	TestScenarios   map[string]string `json:"test_scenarios"`

	// Logging and debugging
	VerboseLogging     bool `json:"verbose_logging"`
	LogWebhookPayloads bool `json:"log_webhook_payloads"`
	LogAPIRequests     bool `json:"log_api_requests"`
	LogAPIResponses    bool `json:"log_api_responses"`
}

// DefaultSandboxConfig returns default sandbox configuration
func DefaultSandboxConfig() *SandboxConfig {
	return &SandboxConfig{
		Enabled:              true,
		APIBase:              "https://api.stripe.com",
		ConnectBase:          "https://connect.stripe.com",
		UploadsBase:          "https://files.stripe.com",
		AutoGenerateTestData: true,
		TestDataSeed:         time.Now().UnixNano(),
		SimulateLatency:      false,
		LatencyRange:         100 * time.Millisecond,
		DefaultTestMode:      TestModeSuccess,
		TestScenarios: map[string]string{
			"success":            TestModeSuccess,
			"failure":            TestModeFailure,
			"requires_action":    TestModeRequiresAction,
			"declined":           TestModeDeclined,
			"insufficient_funds": TestModeInsufficientFunds,
			"expired_card":       TestModeExpiredCard,
			"processing_error":   TestModeProcessingError,
		},
		VerboseLogging:     false,
		LogWebhookPayloads: false,
		LogAPIRequests:     false,
		LogAPIResponses:    false,
	}
}

// SandboxManager manages sandbox mode for the Stripe plugin
type SandboxManager struct {
	plugin         *StripePlugin
	config         *SandboxConfig
	testUtilities  *TestingUtilities
	originalConfig *Config
}

// NewSandboxManager creates a new sandbox manager
func NewSandboxManager(plugin *StripePlugin, config *SandboxConfig) *SandboxManager {
	if config == nil {
		config = DefaultSandboxConfig()
	}

	return &SandboxManager{
		plugin: plugin,
		config: config,
		testUtilities: NewTestingUtilities(plugin, &TestConfig{
			SandboxMode: config.Enabled,
			TestMode:    config.DefaultTestMode,
		}),
		originalConfig: plugin.config,
	}
}

// EnableSandboxMode enables sandbox mode
func (sm *SandboxManager) EnableSandboxMode() error {
	sm.config.Enabled = true

	// Configure Stripe SDK for sandbox mode
	if err := sm.configureStripeSDK(); err != nil {
		return fmt.Errorf("failed to configure Stripe SDK for sandbox mode: %w", err)
	}

	sm.plugin.logger.Info("Sandbox mode enabled", "config", sm.config)
	return nil
}

// DisableSandboxMode disables sandbox mode
func (sm *SandboxManager) DisableSandboxMode() error {
	sm.config.Enabled = false

	// Restore original configuration
	sm.plugin.config = sm.originalConfig

	sm.plugin.logger.Info("Sandbox mode disabled")
	return nil
}

// IsSandboxMode returns true if sandbox mode is enabled
func (sm *SandboxManager) IsSandboxMode() bool {
	return sm.config.Enabled
}

// configureStripeSDK configures the Stripe SDK for sandbox mode
func (sm *SandboxManager) configureStripeSDK() error {
	// Use test keys in sandbox mode
	if !strings.HasPrefix(sm.plugin.config.SecretKey, "sk_test_") {
		return fmt.Errorf("sandbox mode requires test secret key (sk_test_...)")
	}

	if !strings.HasPrefix(sm.plugin.config.PublishableKey, "pk_test_") {
		return fmt.Errorf("sandbox mode requires test publishable key (pk_test_...)")
	}

	// Configure Stripe SDK
	stripe.Key = sm.plugin.config.SecretKey

	// In sandbox mode, we use the default Stripe endpoints
	// Custom endpoints configuration is not needed for testing

	return nil
}

// SetTestMode sets the test mode for sandbox operations
func (sm *SandboxManager) SetTestMode(mode string) error {
	if !sm.config.Enabled {
		return fmt.Errorf("sandbox mode is not enabled")
	}

	// Validate test mode
	validModes := []string{
		TestModeSuccess,
		TestModeFailure,
		TestModeRequiresAction,
		TestModeDeclined,
		TestModeInsufficientFunds,
		TestModeExpiredCard,
		TestModeProcessingError,
	}

	isValid := false
	for _, validMode := range validModes {
		if mode == validMode {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid test mode: %s", mode)
	}

	sm.config.DefaultTestMode = mode
	sm.testUtilities.SetTestMode(mode)

	sm.plugin.logger.Info("Test mode set", "mode", mode)
	return nil
}

// GetTestMode returns the current test mode
func (sm *SandboxManager) GetTestMode() string {
	return sm.config.DefaultTestMode
}

// ProcessPaymentInSandbox processes a payment in sandbox mode
func (sm *SandboxManager) ProcessPaymentInSandbox(ctx context.Context, request *payment.PaymentRequest) (*payment.PaymentResult, error) {
	if !sm.config.Enabled {
		return nil, fmt.Errorf("sandbox mode is not enabled")
	}

	// Simulate latency if enabled
	if sm.config.SimulateLatency {
		time.Sleep(sm.config.LatencyRange)
	}

	// Log request if enabled
	if sm.config.LogAPIRequests {
		sm.plugin.logger.Debug("Sandbox payment request", "request", request)
	}

	// Generate mock response
	result := sm.testUtilities.MockPaymentResult(request)

	// Log response if enabled
	if sm.config.LogAPIResponses {
		sm.plugin.logger.Debug("Sandbox payment response", "result", result)
	}

	return result, nil
}

// ProcessRefundInSandbox processes a refund in sandbox mode
func (sm *SandboxManager) ProcessRefundInSandbox(ctx context.Context, request *payment.RefundRequest) (*payment.RefundResult, error) {
	if !sm.config.Enabled {
		return nil, fmt.Errorf("sandbox mode is not enabled")
	}

	// Simulate latency if enabled
	if sm.config.SimulateLatency {
		time.Sleep(sm.config.LatencyRange)
	}

	// Log request if enabled
	if sm.config.LogAPIRequests {
		sm.plugin.logger.Debug("Sandbox refund request", "request", request)
	}

	// Generate mock response
	result := sm.testUtilities.MockRefundResult(request)

	// Log response if enabled
	if sm.config.LogAPIResponses {
		sm.plugin.logger.Debug("Sandbox refund response", "result", result)
	}

	return result, nil
}

// ProcessSubscriptionInSandbox processes a subscription in sandbox mode
func (sm *SandboxManager) ProcessSubscriptionInSandbox(ctx context.Context, request *payment.SubscriptionRequest) (*payment.SubscriptionResult, error) {
	if !sm.config.Enabled {
		return nil, fmt.Errorf("sandbox mode is not enabled")
	}

	// Simulate latency if enabled
	if sm.config.SimulateLatency {
		time.Sleep(sm.config.LatencyRange)
	}

	// Log request if enabled
	if sm.config.LogAPIRequests {
		sm.plugin.logger.Debug("Sandbox subscription request", "request", request)
	}

	// Generate mock response
	result := sm.testUtilities.MockSubscriptionResult(request)

	// Log response if enabled
	if sm.config.LogAPIResponses {
		sm.plugin.logger.Debug("Sandbox subscription response", "result", result)
	}

	return result, nil
}

// ProcessWebhookInSandbox processes a webhook in sandbox mode
func (sm *SandboxManager) ProcessWebhookInSandbox(ctx context.Context, payload []byte, headers map[string]string) (*payment.WebhookResult, error) {
	if !sm.config.Enabled {
		return nil, fmt.Errorf("sandbox mode is not enabled")
	}

	// Log webhook payload if enabled
	if sm.config.LogWebhookPayloads {
		sm.plugin.logger.Debug("Sandbox webhook payload", "payload", string(payload), "headers", headers)
	}

	// In sandbox mode, we bypass signature verification and generate mock responses
	result := &payment.WebhookResult{
		Provider: "stripe",
		Events: []*payment.WebhookEventResult{
			{
				Event:     "test_event",
				Timestamp: time.Now(),
				Metadata: map[string]interface{}{
					"sandbox_mode": true,
					"test_mode":    sm.config.DefaultTestMode,
				},
			},
		},
	}

	return result, nil
}

// GetSandboxConfiguration returns the current sandbox configuration
func (sm *SandboxManager) GetSandboxConfiguration() *SandboxConfig {
	return sm.config
}

// UpdateSandboxConfiguration updates the sandbox configuration
func (sm *SandboxManager) UpdateSandboxConfiguration(config *SandboxConfig) error {
	if config == nil {
		return fmt.Errorf("sandbox configuration cannot be nil")
	}

	sm.config = config
	sm.testUtilities.SetTestMode(config.DefaultTestMode)

	if config.Enabled {
		if err := sm.configureStripeSDK(); err != nil {
			return fmt.Errorf("failed to configure Stripe SDK: %w", err)
		}
	}

	sm.plugin.logger.Info("Sandbox configuration updated", "config", config)
	return nil
}

// ValidateTestKeys validates that test keys are being used
func (sm *SandboxManager) ValidateTestKeys() error {
	if !strings.HasPrefix(sm.plugin.config.SecretKey, "sk_test_") {
		return fmt.Errorf("secret key must be a test key (sk_test_...) in sandbox mode")
	}

	if !strings.HasPrefix(sm.plugin.config.PublishableKey, "pk_test_") {
		return fmt.Errorf("publishable key must be a test key (pk_test_...) in sandbox mode")
	}

	if sm.plugin.config.WebhookSecret != "" && !strings.HasPrefix(sm.plugin.config.WebhookSecret, "whsec_test_") {
		return fmt.Errorf("webhook secret should be a test webhook secret (whsec_test_...) in sandbox mode")
	}

	return nil
}

// ResetSandboxData resets sandbox test data
func (sm *SandboxManager) ResetSandboxData() {
	sm.testUtilities = NewTestingUtilities(sm.plugin, &TestConfig{
		SandboxMode: sm.config.Enabled,
		TestMode:    sm.config.DefaultTestMode,
	})

	sm.plugin.logger.Info("Sandbox data reset")
}

// GetTestCardNumbers returns all available test card numbers
func (sm *SandboxManager) GetTestCardNumbers() map[string]string {
	return TestCardNumbers
}

// GetTestCardNumber returns the test card number for a specific scenario
func (sm *SandboxManager) GetTestCardNumber(scenario string) string {
	return sm.testUtilities.GetTestCardNumber(scenario)
}

// SetLatencySimulation enables/disables latency simulation
func (sm *SandboxManager) SetLatencySimulation(enabled bool, latencyRange time.Duration) {
	sm.config.SimulateLatency = enabled
	sm.config.LatencyRange = latencyRange
	sm.testUtilities.SetDelayResponses(latencyRange)

	sm.plugin.logger.Info("Latency simulation updated", "enabled", enabled, "range", latencyRange)
}

// SetVerboseLogging enables/disables verbose logging
func (sm *SandboxManager) SetVerboseLogging(enabled bool) {
	sm.config.VerboseLogging = enabled
	sm.plugin.logger.Info("Verbose logging updated", "enabled", enabled)
}

// SetWebhookPayloadLogging enables/disables webhook payload logging
func (sm *SandboxManager) SetWebhookPayloadLogging(enabled bool) {
	sm.config.LogWebhookPayloads = enabled
	sm.plugin.logger.Info("Webhook payload logging updated", "enabled", enabled)
}

// SetAPIRequestLogging enables/disables API request logging
func (sm *SandboxManager) SetAPIRequestLogging(enabled bool) {
	sm.config.LogAPIRequests = enabled
	sm.plugin.logger.Info("API request logging updated", "enabled", enabled)
}

// SetAPIResponseLogging enables/disables API response logging
func (sm *SandboxManager) SetAPIResponseLogging(enabled bool) {
	sm.config.LogAPIResponses = enabled
	sm.plugin.logger.Info("API response logging updated", "enabled", enabled)
}

// LoadSandboxConfigFromEnv loads sandbox configuration from environment variables
func (sm *SandboxManager) LoadSandboxConfigFromEnv() error {
	config := DefaultSandboxConfig()

	// Load from environment variables
	if enabled := os.Getenv("STRIPE_SANDBOX_ENABLED"); enabled != "" {
		config.Enabled = enabled == "true"
	}

	if testMode := os.Getenv("STRIPE_SANDBOX_TEST_MODE"); testMode != "" {
		config.DefaultTestMode = testMode
	}

	if verboseLogging := os.Getenv("STRIPE_SANDBOX_VERBOSE_LOGGING"); verboseLogging != "" {
		config.VerboseLogging = verboseLogging == "true"
	}

	if logWebhooks := os.Getenv("STRIPE_SANDBOX_LOG_WEBHOOKS"); logWebhooks != "" {
		config.LogWebhookPayloads = logWebhooks == "true"
	}

	if logRequests := os.Getenv("STRIPE_SANDBOX_LOG_REQUESTS"); logRequests != "" {
		config.LogAPIRequests = logRequests == "true"
	}

	if logResponses := os.Getenv("STRIPE_SANDBOX_LOG_RESPONSES"); logResponses != "" {
		config.LogAPIResponses = logResponses == "true"
	}

	if simulateLatency := os.Getenv("STRIPE_SANDBOX_SIMULATE_LATENCY"); simulateLatency != "" {
		config.SimulateLatency = simulateLatency == "true"
	}

	return sm.UpdateSandboxConfiguration(config)
}

// ExportSandboxConfiguration exports sandbox configuration to environment variables
func (sm *SandboxManager) ExportSandboxConfiguration() map[string]string {
	envVars := make(map[string]string)

	envVars["STRIPE_SANDBOX_ENABLED"] = fmt.Sprintf("%t", sm.config.Enabled)
	envVars["STRIPE_SANDBOX_TEST_MODE"] = sm.config.DefaultTestMode
	envVars["STRIPE_SANDBOX_VERBOSE_LOGGING"] = fmt.Sprintf("%t", sm.config.VerboseLogging)
	envVars["STRIPE_SANDBOX_LOG_WEBHOOKS"] = fmt.Sprintf("%t", sm.config.LogWebhookPayloads)
	envVars["STRIPE_SANDBOX_LOG_REQUESTS"] = fmt.Sprintf("%t", sm.config.LogAPIRequests)
	envVars["STRIPE_SANDBOX_LOG_RESPONSES"] = fmt.Sprintf("%t", sm.config.LogAPIResponses)
	envVars["STRIPE_SANDBOX_SIMULATE_LATENCY"] = fmt.Sprintf("%t", sm.config.SimulateLatency)

	return envVars
}
