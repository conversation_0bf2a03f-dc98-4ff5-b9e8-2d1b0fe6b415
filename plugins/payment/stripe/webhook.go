package stripe

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/webhook"

	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// HandleWebhook processes webhook events from Stripe
func (p *StripePlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*payment.WebhookResult, error) {
	// Get Stripe signature
	signature := headers["Stripe-Signature"]
	if signature == "" {
		return nil, payment.NewPaymentError(payment.ErrorCodeWebhookFailed, "missing Stripe signature")
	}

	// Verify webhook signature
	event, err := webhook.ConstructEvent(payload, signature, p.config.WebhookSecret)
	if err != nil {
		p.logger.Error("Webhook signature verification failed", "error", err)
		return nil, payment.NewPaymentError(payment.ErrorCodeWebhookFailed, fmt.Sprintf("webhook signature verification failed: %v", err))
	}

	p.logger.Debug("Received Stripe webhook", "event_type", event.Type, "event_id", event.ID)

	// Process webhook event
	result := p.processWebhookEvent(ctx, &event)

	return &payment.WebhookResult{
		Provider: "stripe",
		Events:   []*payment.WebhookEventResult{result},
	}, nil
}

// processWebhookEvent processes a single webhook event
func (p *StripePlugin) processWebhookEvent(ctx context.Context, event *stripe.Event) *payment.WebhookEventResult {
	switch event.Type {
	case "payment_intent.succeeded":
		return p.handlePaymentIntentSucceeded(event)
	case "payment_intent.payment_failed":
		return p.handlePaymentIntentFailed(event)
	case "payment_intent.requires_action":
		return p.handlePaymentIntentRequiresAction(event)
	case "payment_intent.canceled":
		return p.handlePaymentIntentCanceled(event)
	case "invoice.payment_succeeded":
		return p.handleInvoicePaymentSucceeded(event)
	case "invoice.payment_failed":
		return p.handleInvoicePaymentFailed(event)
	case "customer.subscription.created":
		return p.handleSubscriptionCreated(event)
	case "customer.subscription.updated":
		return p.handleSubscriptionUpdated(event)
	case "customer.subscription.deleted":
		return p.handleSubscriptionDeleted(event)
	case "customer.subscription.trial_will_end":
		return p.handleSubscriptionTrialWillEnd(event)
	case "charge.dispute.created":
		return p.handleChargeDisputeCreated(event)
	default:
		p.logger.Debug("Unhandled webhook event", "event_type", event.Type)
		return &payment.WebhookEventResult{
			Event:     string(event.Type),
			Timestamp: time.Unix(event.Created, 0),
			Metadata: map[string]interface{}{
				"stripe_event_id": event.ID,
				"unhandled":       true,
			},
		}
	}
}

// handlePaymentIntentSucceeded handles successful payment intent events
func (p *StripePlugin) handlePaymentIntentSucceeded(event *stripe.Event) *payment.WebhookEventResult {
	var pi stripe.PaymentIntent
	if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
		p.logger.Error("Failed to unmarshal payment intent", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse payment intent")
	}

	p.logger.Info("Payment intent succeeded", "payment_intent_id", pi.ID, "amount", pi.Amount)

	return &payment.WebhookEventResult{
		PaymentID: pi.ID,
		Event:     "payment_succeeded",
		Timestamp: time.Unix(event.Created, 0),
		Status:    payment.PaymentStatusSucceeded,
		Amount:    p.convertFromMinorUnits(pi.Amount, string(pi.Currency)),
		Currency:  strings.ToUpper(string(pi.Currency)),
		Metadata: map[string]interface{}{
			"stripe_event_id":     event.ID,
			"payment_intent_id":   pi.ID,
			"payment_method_id":   pi.PaymentMethod,
			"confirmation_method": pi.ConfirmationMethod,
		},
	}
}

// handlePaymentIntentFailed handles failed payment intent events
func (p *StripePlugin) handlePaymentIntentFailed(event *stripe.Event) *payment.WebhookEventResult {
	var pi stripe.PaymentIntent
	if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
		p.logger.Error("Failed to unmarshal payment intent", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse payment intent")
	}

	p.logger.Warn("Payment intent failed", "payment_intent_id", pi.ID, "error", pi.LastPaymentError)

	reason := "Payment failed"
	if pi.LastPaymentError != nil {
		reason = pi.LastPaymentError.Msg
	}

	return &payment.WebhookEventResult{
		PaymentID: pi.ID,
		Event:     "payment_failed",
		Timestamp: time.Unix(event.Created, 0),
		Status:    payment.PaymentStatusFailed,
		Reason:    reason,
		Metadata: map[string]interface{}{
			"stripe_event_id":   event.ID,
			"payment_intent_id": pi.ID,
			"error_code":        pi.LastPaymentError.Code,
			"error_type":        pi.LastPaymentError.Type,
		},
	}
}

// handlePaymentIntentRequiresAction handles payment intents that require action
func (p *StripePlugin) handlePaymentIntentRequiresAction(event *stripe.Event) *payment.WebhookEventResult {
	var pi stripe.PaymentIntent
	if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
		p.logger.Error("Failed to unmarshal payment intent", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse payment intent")
	}

	p.logger.Info("Payment intent requires action", "payment_intent_id", pi.ID)

	return &payment.WebhookEventResult{
		PaymentID: pi.ID,
		Event:     "payment_requires_action",
		Timestamp: time.Unix(event.Created, 0),
		Status:    payment.PaymentStatusRequiresAction,
		Metadata: map[string]interface{}{
			"stripe_event_id":   event.ID,
			"payment_intent_id": pi.ID,
			"client_secret":     pi.ClientSecret,
		},
	}
}

// handlePaymentIntentCanceled handles canceled payment intent events
func (p *StripePlugin) handlePaymentIntentCanceled(event *stripe.Event) *payment.WebhookEventResult {
	var pi stripe.PaymentIntent
	if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
		p.logger.Error("Failed to unmarshal payment intent", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse payment intent")
	}

	p.logger.Info("Payment intent canceled", "payment_intent_id", pi.ID)

	return &payment.WebhookEventResult{
		PaymentID: pi.ID,
		Event:     "payment_canceled",
		Timestamp: time.Unix(event.Created, 0),
		Status:    payment.PaymentStatusCanceled,
		Metadata: map[string]interface{}{
			"stripe_event_id":   event.ID,
			"payment_intent_id": pi.ID,
		},
	}
}

// handleInvoicePaymentSucceeded handles successful invoice payment events
func (p *StripePlugin) handleInvoicePaymentSucceeded(event *stripe.Event) *payment.WebhookEventResult {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		p.logger.Error("Failed to unmarshal invoice", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse invoice")
	}

	p.logger.Info("Invoice payment succeeded", "invoice_id", invoice.ID, "subscription_id", invoice.Subscription)

	return &payment.WebhookEventResult{
		PaymentID:      invoice.PaymentIntent.ID,
		SubscriptionID: invoice.Subscription.ID,
		Event:          "subscription_payment_succeeded",
		Timestamp:      time.Unix(event.Created, 0),
		Status:         payment.PaymentStatusSucceeded,
		Amount:         p.convertFromMinorUnits(invoice.AmountPaid, string(invoice.Currency)),
		Currency:       strings.ToUpper(string(invoice.Currency)),
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"invoice_id":      invoice.ID,
			"subscription_id": invoice.Subscription.ID,
			"billing_reason":  invoice.BillingReason,
		},
	}
}

// handleInvoicePaymentFailed handles failed invoice payment events
func (p *StripePlugin) handleInvoicePaymentFailed(event *stripe.Event) *payment.WebhookEventResult {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		p.logger.Error("Failed to unmarshal invoice", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse invoice")
	}

	p.logger.Warn("Invoice payment failed", "invoice_id", invoice.ID, "subscription_id", invoice.Subscription)

	return &payment.WebhookEventResult{
		PaymentID:      invoice.PaymentIntent.ID,
		SubscriptionID: invoice.Subscription.ID,
		Event:          "subscription_payment_failed",
		Timestamp:      time.Unix(event.Created, 0),
		Status:         payment.PaymentStatusFailed,
		Amount:         p.convertFromMinorUnits(invoice.AmountDue, string(invoice.Currency)),
		Currency:       strings.ToUpper(string(invoice.Currency)),
		Reason:         "Invoice payment failed",
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"invoice_id":      invoice.ID,
			"subscription_id": invoice.Subscription.ID,
			"billing_reason":  invoice.BillingReason,
		},
	}
}

// handleSubscriptionCreated handles subscription creation events
func (p *StripePlugin) handleSubscriptionCreated(event *stripe.Event) *payment.WebhookEventResult {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		p.logger.Error("Failed to unmarshal subscription", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse subscription")
	}

	p.logger.Info("Subscription created", "subscription_id", subscription.ID, "customer_id", subscription.Customer.ID)

	return &payment.WebhookEventResult{
		SubscriptionID: subscription.ID,
		Event:          "subscription_created",
		Timestamp:      time.Unix(event.Created, 0),
		Metadata: map[string]interface{}{
			"stripe_event_id":      event.ID,
			"subscription_id":      subscription.ID,
			"customer_id":          subscription.Customer.ID,
			"status":               subscription.Status,
			"current_period_start": subscription.CurrentPeriodStart,
			"current_period_end":   subscription.CurrentPeriodEnd,
			"trial_end":            subscription.TrialEnd,
		},
	}
}

// handleSubscriptionUpdated handles subscription update events
func (p *StripePlugin) handleSubscriptionUpdated(event *stripe.Event) *payment.WebhookEventResult {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		p.logger.Error("Failed to unmarshal subscription", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse subscription")
	}

	p.logger.Info("Subscription updated", "subscription_id", subscription.ID, "status", subscription.Status)

	return &payment.WebhookEventResult{
		SubscriptionID: subscription.ID,
		Event:          "subscription_updated",
		Timestamp:      time.Unix(event.Created, 0),
		Metadata: map[string]interface{}{
			"stripe_event_id":      event.ID,
			"subscription_id":      subscription.ID,
			"customer_id":          subscription.Customer.ID,
			"status":               subscription.Status,
			"current_period_start": subscription.CurrentPeriodStart,
			"current_period_end":   subscription.CurrentPeriodEnd,
		},
	}
}

// handleSubscriptionDeleted handles subscription deletion events
func (p *StripePlugin) handleSubscriptionDeleted(event *stripe.Event) *payment.WebhookEventResult {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		p.logger.Error("Failed to unmarshal subscription", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse subscription")
	}

	p.logger.Info("Subscription deleted", "subscription_id", subscription.ID)

	return &payment.WebhookEventResult{
		SubscriptionID: subscription.ID,
		Event:          "subscription_canceled",
		Timestamp:      time.Unix(event.Created, 0),
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"subscription_id": subscription.ID,
			"customer_id":     subscription.Customer.ID,
			"canceled_at":     subscription.CanceledAt,
		},
	}
}

// handleSubscriptionTrialWillEnd handles trial ending events
func (p *StripePlugin) handleSubscriptionTrialWillEnd(event *stripe.Event) *payment.WebhookEventResult {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		p.logger.Error("Failed to unmarshal subscription", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse subscription")
	}

	p.logger.Info("Subscription trial will end", "subscription_id", subscription.ID, "trial_end", subscription.TrialEnd)

	return &payment.WebhookEventResult{
		SubscriptionID: subscription.ID,
		Event:          "subscription_trial_will_end",
		Timestamp:      time.Unix(event.Created, 0),
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"subscription_id": subscription.ID,
			"customer_id":     subscription.Customer.ID,
			"trial_end":       subscription.TrialEnd,
		},
	}
}

// handleChargeDisputeCreated handles dispute creation events
func (p *StripePlugin) handleChargeDisputeCreated(event *stripe.Event) *payment.WebhookEventResult {
	var dispute stripe.Dispute
	if err := json.Unmarshal(event.Data.Raw, &dispute); err != nil {
		p.logger.Error("Failed to unmarshal dispute", "error", err)
		return p.createErrorWebhookResult(event, "failed to parse dispute")
	}

	p.logger.Warn("Charge dispute created", "dispute_id", dispute.ID, "charge_id", dispute.Charge.ID)

	return &payment.WebhookEventResult{
		PaymentID: dispute.Charge.PaymentIntent.ID,
		Event:     "charge_dispute_created",
		Timestamp: time.Unix(event.Created, 0),
		Amount:    p.convertFromMinorUnits(dispute.Amount, string(dispute.Currency)),
		Currency:  strings.ToUpper(string(dispute.Currency)),
		Reason:    string(dispute.Reason),
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"dispute_id":      dispute.ID,
			"charge_id":       dispute.Charge.ID,
			"dispute_reason":  string(dispute.Reason),
			"dispute_status":  string(dispute.Status),
		},
	}
}

// createErrorWebhookResult creates a webhook result for parsing errors
func (p *StripePlugin) createErrorWebhookResult(event *stripe.Event, errorMsg string) *payment.WebhookEventResult {
	return &payment.WebhookEventResult{
		Event:     string(event.Type),
		Timestamp: time.Unix(event.Created, 0),
		Reason:    errorMsg,
		Metadata: map[string]interface{}{
			"stripe_event_id": event.ID,
			"error":           errorMsg,
		},
	}
}
