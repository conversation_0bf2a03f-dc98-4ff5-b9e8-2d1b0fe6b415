package stripe

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/customer"
	"github.com/stripe/stripe-go/v76/paymentintent"
	"github.com/stripe/stripe-go/v76/paymentmethod"
	"github.com/stripe/stripe-go/v76/refund"
	"github.com/stripe/stripe-go/v76/subscription"

	"github.com/tranthanhloi/wn-api-v3/pkg/plugin"
	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// StripePlugin implements the PaymentPlugin interface for Stripe
type StripePlugin struct {
	config *Config
	logger plugin.Logger
}

// NewStripePlugin creates a new Stripe payment plugin
func NewStripePlugin(config map[string]interface{}, logger plugin.Logger) (payment.PaymentPlugin, error) {
	stripeConfig, err := parseConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Stripe config: %w", err)
	}

	if logger == nil {
		logger = plugin.NewDefaultLogger()
	}

	plugin := &StripePlugin{
		config: stripeConfig,
		logger: logger,
	}

	// Initialize Stripe SDK
	stripe.Key = stripeConfig.SecretKey

	return plugin, nil
}

// ProcessPayment processes a one-time payment
func (p *StripePlugin) ProcessPayment(ctx context.Context, request *payment.PaymentRequest) (*payment.PaymentResult, error) {
	// Validate currency
	if err := p.validateCurrency(request.Currency); err != nil {
		return nil, err
	}

	// Create payment intent
	params := &stripe.PaymentIntentParams{
		Amount:             stripe.Int64(p.convertToMinorUnits(request.Amount, request.Currency)),
		Currency:           stripe.String(strings.ToLower(request.Currency)),
		PaymentMethodTypes: stripe.StringSlice(p.config.PaymentMethods),
		CaptureMethod:      stripe.String(p.config.CaptureMethod),
	}

	// Set customer if provided
	if request.CustomerID != "" {
		params.Customer = stripe.String(request.CustomerID)
	}

	// Set payment method if provided
	if request.PaymentMethodID != "" {
		params.PaymentMethod = stripe.String(request.PaymentMethodID)
		params.ConfirmationMethod = stripe.String("manual")
		params.Confirm = stripe.Bool(true)
	}

	// Set description
	if request.Description != "" {
		params.Description = stripe.String(request.Description)
	}

	// Add metadata
	if request.Metadata != nil {
		params.Metadata = make(map[string]string)
		for key, value := range request.Metadata {
			params.Metadata[key] = fmt.Sprintf("%v", value)
		}
	}

	// Set 3D Secure requirements
	if p.config.Require3DS == "required" {
		params.PaymentMethodOptions = &stripe.PaymentIntentPaymentMethodOptionsParams{
			Card: &stripe.PaymentIntentPaymentMethodOptionsCardParams{
				RequestThreeDSecure: stripe.String("required"),
			},
		}
	}

	// Create payment intent
	pi, err := paymentintent.New(params)
	if err != nil {
		p.logger.Error("Stripe payment intent creation failed", "error", err)
		return nil, p.handleStripeError(err)
	}

	p.logger.Info("Stripe payment intent created", "payment_intent_id", pi.ID, "amount", request.Amount)

	return &payment.PaymentResult{
		PaymentID:    pi.ID,
		Status:       p.mapStripePaymentStatus(pi.Status),
		Amount:       request.Amount,
		Currency:     strings.ToUpper(string(pi.Currency)),
		ProviderID:   "stripe",
		ClientSecret: pi.ClientSecret,
		Metadata: map[string]interface{}{
			"payment_intent_id": pi.ID,
			"requires_action":   pi.Status == stripe.PaymentIntentStatusRequiresAction,
		},
		CreatedAt: timePtr(time.Unix(pi.Created, 0)),
	}, nil
}

// RefundPayment refunds a processed payment
func (p *StripePlugin) RefundPayment(ctx context.Context, request *payment.RefundRequest) (*payment.RefundResult, error) {
	params := &stripe.RefundParams{
		PaymentIntent: stripe.String(request.PaymentID),
	}

	// Set amount for partial refund
	if request.Amount != nil {
		params.Amount = stripe.Int64(p.convertToMinorUnits(*request.Amount, ""))
	}

	// Set reason
	if request.Reason != "" {
		params.Reason = stripe.String(request.Reason)
	}

	// Add metadata
	if request.Metadata != nil {
		params.Metadata = make(map[string]string)
		for key, value := range request.Metadata {
			params.Metadata[key] = fmt.Sprintf("%v", value)
		}
	}

	// Create refund
	r, err := refund.New(params)
	if err != nil {
		p.logger.Error("Stripe refund creation failed", "error", err)
		return nil, p.handleStripeError(err)
	}

	p.logger.Info("Stripe refund created", "refund_id", r.ID, "payment_intent", request.PaymentID)

	return &payment.RefundResult{
		RefundID:  r.ID,
		PaymentID: request.PaymentID,
		Status:    p.mapStripeRefundStatus(r.Status),
		Amount:    p.convertFromMinorUnits(r.Amount, string(r.Currency)),
		Currency:  strings.ToUpper(string(r.Currency)),
		Reason:    request.Reason,
		CreatedAt: timePtr(time.Unix(r.Created, 0)),
	}, nil
}

// CreateSubscription creates a recurring subscription
func (p *StripePlugin) CreateSubscription(ctx context.Context, request *payment.SubscriptionRequest) (*payment.SubscriptionResult, error) {
	// Create or retrieve customer
	customerID, err := p.getOrCreateCustomer(request.CustomerEmail, request.CustomerName)
	if err != nil {
		return nil, fmt.Errorf("customer creation failed: %w", err)
	}

	// Attach payment method to customer if provided
	if request.PaymentMethodID != "" {
		_, err = paymentmethod.Attach(request.PaymentMethodID, &stripe.PaymentMethodAttachParams{
			Customer: stripe.String(customerID),
		})
		if err != nil {
			p.logger.Error("Payment method attachment failed", "error", err)
			return nil, p.handleStripeError(err)
		}

		// Set as default payment method
		_, err = customer.Update(customerID, &stripe.CustomerParams{
			InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
				DefaultPaymentMethod: stripe.String(request.PaymentMethodID),
			},
		})
		if err != nil {
			p.logger.Error("Default payment method update failed", "error", err)
			return nil, p.handleStripeError(err)
		}
	}

	// Create subscription
	params := &stripe.SubscriptionParams{
		Customer: stripe.String(customerID),
		Items: []*stripe.SubscriptionItemsParams{
			{
				Price: stripe.String(request.PriceID),
			},
		},
	}

	// Add trial period if specified
	if request.TrialDays > 0 {
		trialEnd := time.Now().AddDate(0, 0, request.TrialDays).Unix()
		params.TrialEnd = stripe.Int64(trialEnd)
	}

	// Add metadata
	if request.Metadata != nil {
		params.Metadata = make(map[string]string)
		for key, value := range request.Metadata {
			params.Metadata[key] = fmt.Sprintf("%v", value)
		}
	}

	// Create subscription
	sub, err := subscription.New(params)
	if err != nil {
		p.logger.Error("Stripe subscription creation failed", "error", err)
		return nil, p.handleStripeError(err)
	}

	p.logger.Info("Stripe subscription created", "subscription_id", sub.ID, "customer_id", customerID)

	result := &payment.SubscriptionResult{
		SubscriptionID: sub.ID,
		Status:         p.mapStripeSubscriptionStatus(sub.Status),
		CustomerID:     customerID,
		PriceID:        request.PriceID,
		ProviderID:     "stripe",
		CreatedAt:      timePtr(time.Unix(sub.Created, 0)),
	}

	if sub.CurrentPeriodStart != 0 {
		result.CurrentPeriodStart = timePtr(time.Unix(sub.CurrentPeriodStart, 0))
	}
	if sub.CurrentPeriodEnd != 0 {
		result.CurrentPeriodEnd = timePtr(time.Unix(sub.CurrentPeriodEnd, 0))
	}
	if sub.TrialEnd != 0 {
		result.TrialEnd = timePtr(time.Unix(sub.TrialEnd, 0))
	}

	return result, nil
}

// UpdateSubscription updates an existing subscription
func (p *StripePlugin) UpdateSubscription(ctx context.Context, request *payment.SubscriptionUpdateRequest) (*payment.SubscriptionResult, error) {
	params := &stripe.SubscriptionParams{}

	// Update price if provided
	if request.PriceID != "" {
		// Get current subscription to update items
		currentSub, err := subscription.Get(request.SubscriptionID, nil)
		if err != nil {
			return nil, p.handleStripeError(err)
		}

		// Update subscription items
		params.Items = []*stripe.SubscriptionItemsParams{
			{
				ID:    stripe.String(currentSub.Items.Data[0].ID),
				Price: stripe.String(request.PriceID),
			},
		}
	}

	// Update payment method if provided
	if request.PaymentMethodID != "" {
		params.DefaultPaymentMethod = stripe.String(request.PaymentMethodID)
	}

	// Add metadata
	if request.Metadata != nil {
		params.Metadata = make(map[string]string)
		for key, value := range request.Metadata {
			params.Metadata[key] = fmt.Sprintf("%v", value)
		}
	}

	// Update subscription
	sub, err := subscription.Update(request.SubscriptionID, params)
	if err != nil {
		p.logger.Error("Stripe subscription update failed", "error", err)
		return nil, p.handleStripeError(err)
	}

	p.logger.Info("Stripe subscription updated", "subscription_id", sub.ID)

	result := &payment.SubscriptionResult{
		SubscriptionID: sub.ID,
		Status:         p.mapStripeSubscriptionStatus(sub.Status),
		CustomerID:     sub.Customer.ID,
		PriceID:        request.PriceID,
		ProviderID:     "stripe",
	}

	if sub.CurrentPeriodStart != 0 {
		result.CurrentPeriodStart = timePtr(time.Unix(sub.CurrentPeriodStart, 0))
	}
	if sub.CurrentPeriodEnd != 0 {
		result.CurrentPeriodEnd = timePtr(time.Unix(sub.CurrentPeriodEnd, 0))
	}

	return result, nil
}

// CancelSubscription cancels a subscription
func (p *StripePlugin) CancelSubscription(ctx context.Context, subscriptionID string) error {
	_, err := subscription.Cancel(subscriptionID, nil)
	if err != nil {
		p.logger.Error("Stripe subscription cancellation failed", "error", err)
		return p.handleStripeError(err)
	}

	p.logger.Info("Stripe subscription cancelled", "subscription_id", subscriptionID)
	return nil
}

// GetPaymentStatus retrieves the current status of a payment
func (p *StripePlugin) GetPaymentStatus(ctx context.Context, paymentID string) (*payment.PaymentStatus, error) {
	pi, err := paymentintent.Get(paymentID, nil)
	if err != nil {
		return nil, p.handleStripeError(err)
	}

	return &payment.PaymentStatus{
		PaymentID: pi.ID,
		Status:    p.mapStripePaymentStatus(pi.Status),
		Amount:    p.convertFromMinorUnits(pi.Amount, string(pi.Currency)),
		Currency:  strings.ToUpper(string(pi.Currency)),
		UpdatedAt: timePtr(time.Unix(pi.Created, 0)),
	}, nil
}

// GetSubscriptionStatus retrieves the current status of a subscription
func (p *StripePlugin) GetSubscriptionStatus(ctx context.Context, subscriptionID string) (*payment.SubscriptionStatus, error) {
	sub, err := subscription.Get(subscriptionID, nil)
	if err != nil {
		return nil, p.handleStripeError(err)
	}

	result := &payment.SubscriptionStatus{
		SubscriptionID: sub.ID,
		Status:         p.mapStripeSubscriptionStatus(sub.Status),
		UpdatedAt:      timePtr(time.Unix(sub.Created, 0)),
	}

	if sub.CurrentPeriodStart != 0 {
		result.CurrentPeriodStart = timePtr(time.Unix(sub.CurrentPeriodStart, 0))
	}
	if sub.CurrentPeriodEnd != 0 {
		result.CurrentPeriodEnd = timePtr(time.Unix(sub.CurrentPeriodEnd, 0))
	}

	return result, nil
}

// GetSupportedCurrencies returns the list of supported currencies
func (p *StripePlugin) GetSupportedCurrencies() []string {
	return p.config.SupportedCurrencies
}

// GetSupportedPaymentMethods returns the list of supported payment methods
func (p *StripePlugin) GetSupportedPaymentMethods() []string {
	return p.config.PaymentMethods
}

// ValidateConfiguration validates the plugin configuration
func (p *StripePlugin) ValidateConfiguration(config map[string]interface{}) error {
	stripeConfig, err := parseConfig(config)
	if err != nil {
		return err
	}

	// Validate required fields
	if stripeConfig.SecretKey == "" {
		return fmt.Errorf("secret_key is required")
	}

	if stripeConfig.PublishableKey == "" {
		return fmt.Errorf("publishable_key is required")
	}

	// Validate currencies
	if len(stripeConfig.SupportedCurrencies) == 0 {
		return fmt.Errorf("at least one supported currency is required")
	}

	// Validate payment methods
	if len(stripeConfig.PaymentMethods) == 0 {
		return fmt.Errorf("at least one payment method is required")
	}

	return nil
}

// Helper functions

func timePtr(t time.Time) *time.Time {
	return &t
}
