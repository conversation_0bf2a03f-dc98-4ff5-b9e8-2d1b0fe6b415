package stripe

import (
	"context"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/tranthanhloi/wn-api-v3/pkg/plugin"
	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// TestStripePlugin contains tests for the Stripe payment plugin
func TestStripePlugin(t *testing.T) {
	// Setup test plugin
	plugin := createTestPlugin(t)
	testUtils := NewTestingUtilities(plugin, &TestConfig{
		SandboxMode: true,
		TestMode:    TestModeSuccess,
	})

	t.Run("Plugin Info", testPluginInfo(plugin))
	t.Run("Plugin Health", testPluginHealth(plugin))
	t.Run("Payment Processing", testPaymentProcessing(plugin, testUtils))
	t.Run("Refund Processing", testRefundProcessing(plugin, testUtils))
	t.Run("Subscription Management", testSubscriptionManagement(plugin, testUtils))
	t.Run("Webhook Processing", testWebhookProcessing(plugin, testUtils))
	t.Run("Error Handling", testErrorHandling(plugin, testUtils))
}

// createTestPlugin creates a test instance of the Stripe plugin
func createTestPlugin(t *testing.T) *StripePlugin {
	config := &Config{
		SecretKey:           "sk_test_123456789",
		PublishableKey:      "pk_test_123456789",
		WebhookSecret:       "whsec_test_123456789",
		SandboxMode:         true,
		Currency:            "USD",
		Timeout:             30,
		PaymentMethods:      []string{"card"},
		SupportedCurrencies: []string{"usd", "eur", "gbp"},
	}

	plugin := &StripePlugin{
		config: config,
		logger: plugin.NewDefaultLogger(),
	}

	return plugin
}

// testPluginInfo tests the plugin info functionality
func testPluginInfo(plugin *StripePlugin) func(t *testing.T) {
	return func(t *testing.T) {
		info := StripePluginInfo()

		if info.Name != "Stripe Payment Gateway" {
			t.Errorf("Expected plugin name 'Stripe Payment Gateway', got '%s'", info.Name)
		}

		if info.Version == "" {
			t.Error("Plugin version should not be empty")
		}

		if info.Description == "" {
			t.Error("Plugin description should not be empty")
		}

		if info.Author == "" {
			t.Error("Plugin author should not be empty")
		}

		if len(info.Config) == 0 {
			t.Error("Plugin config should not be empty")
		}
	}
}

// testPluginHealth tests the plugin health check
func testPluginHealth(plugin *StripePlugin) func(t *testing.T) {
	return func(t *testing.T) {
		// Test validation - this is what we can test with the current interface
		err := plugin.ValidateConfiguration(map[string]interface{}{
			"secret_key":      "sk_test_123456789",
			"publishable_key": "pk_test_123456789",
		})

		if err != nil {
			t.Errorf("Configuration validation failed: %v", err)
		}

		// Test supported currencies
		currencies := plugin.GetSupportedCurrencies()
		if len(currencies) == 0 {
			t.Error("Expected supported currencies")
		}

		// Test supported payment methods
		methods := plugin.GetSupportedPaymentMethods()
		if len(methods) == 0 {
			t.Error("Expected supported payment methods")
		}
	}
}

// testPaymentProcessing tests various payment processing scenarios
func testPaymentProcessing(plugin *StripePlugin, testUtils *TestingUtilities) func(t *testing.T) {
	return func(t *testing.T) {
		ctx := context.Background()
		runner := NewTestRunner(testUtils)

		// Test successful payment
		t.Run("Successful Payment", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 100.00, "USD", TestModeSuccess)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusSucceeded {
				t.Errorf("Expected payment status 'succeeded', got '%s'", result.Status)
			}

			if !result.Amount.Equal(decimal.NewFromFloat(100.00)) {
				t.Errorf("Expected amount 100.00, got %s", result.Amount.String())
			}

			if result.Currency != "USD" {
				t.Errorf("Expected currency 'USD', got '%s'", result.Currency)
			}

			if result.ProviderID != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.ProviderID)
			}
		})

		// Test failed payment
		t.Run("Failed Payment", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 50.00, "USD", TestModeFailure)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusFailed {
				t.Errorf("Expected payment status 'failed', got '%s'", result.Status)
			}

			if result.Metadata["error_code"] == nil {
				t.Error("Expected error code for failed payment")
			}

			if result.Metadata["error_message"] == nil {
				t.Error("Expected error message for failed payment")
			}
		})

		// Test payment requiring action
		t.Run("Payment Requires Action", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 75.00, "USD", TestModeRequiresAction)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusRequiresAction {
				t.Errorf("Expected payment status 'requires_action', got '%s'", result.Status)
			}

			if result.ClientSecret == "" {
				t.Error("Expected client secret for payment requiring action")
			}

			if result.Metadata["next_action"] == nil {
				t.Error("Expected next action for payment requiring action")
			}
		})

		// Test declined payment
		t.Run("Declined Payment", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 25.00, "USD", TestModeDeclined)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusFailed {
				t.Errorf("Expected payment status 'failed', got '%s'", result.Status)
			}
		})

		// Test insufficient funds
		t.Run("Insufficient Funds", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 200.00, "USD", TestModeInsufficientFunds)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusFailed {
				t.Errorf("Expected payment status 'failed', got '%s'", result.Status)
			}

			if result.Metadata["error_code"] != "insufficient_funds" {
				t.Errorf("Expected error code 'insufficient_funds', got '%s'", result.Metadata["error_code"])
			}
		})

		// Test expired card
		t.Run("Expired Card", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 150.00, "USD", TestModeExpiredCard)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusFailed {
				t.Errorf("Expected payment status 'failed', got '%s'", result.Status)
			}

			if result.Metadata["error_code"] != "expired_card" {
				t.Errorf("Expected error code 'expired_card', got '%s'", result.Metadata["error_code"])
			}
		})
	}
}

// testRefundProcessing tests refund processing scenarios
func testRefundProcessing(plugin *StripePlugin, testUtils *TestingUtilities) func(t *testing.T) {
	return func(t *testing.T) {
		ctx := context.Background()
		runner := NewTestRunner(testUtils)

		// Test successful refund
		t.Run("Successful Refund", func(t *testing.T) {
			result, err := runner.RunRefundTest(ctx, "pi_test_123456789", 100.00, TestModeSuccess)
			if err != nil {
				t.Fatalf("Refund test failed: %v", err)
			}

			if result.Status != payment.RefundStatusSucceeded {
				t.Errorf("Expected refund status 'succeeded', got '%s'", result.Status)
			}

			if !result.Amount.Equal(decimal.NewFromFloat(100.00)) {
				t.Errorf("Expected refund amount 100.00, got %s", result.Amount.String())
			}

			// RefundResult doesn't have a ProviderID field
			// Provider information is maintained in the system context
		})

		// Test failed refund
		t.Run("Failed Refund", func(t *testing.T) {
			result, err := runner.RunRefundTest(ctx, "pi_test_123456789", 50.00, TestModeFailure)
			if err != nil {
				t.Fatalf("Refund test failed: %v", err)
			}

			if result.Status != payment.RefundStatusFailed {
				t.Errorf("Expected refund status 'failed', got '%s'", result.Status)
			}

			if result.Metadata["error_code"] == nil {
				t.Error("Expected error code for failed refund")
			}

			if result.Metadata["error_message"] == nil {
				t.Error("Expected error message for failed refund")
			}
		})
	}
}

// testSubscriptionManagement tests subscription management scenarios
func testSubscriptionManagement(plugin *StripePlugin, testUtils *TestingUtilities) func(t *testing.T) {
	return func(t *testing.T) {
		ctx := context.Background()
		runner := NewTestRunner(testUtils)

		// Test successful subscription creation
		t.Run("Successful Subscription", func(t *testing.T) {
			result, err := runner.RunSubscriptionTest(ctx, "cus_test_123456789", "plan_test_123456789", TestModeSuccess)
			if err != nil {
				t.Fatalf("Subscription test failed: %v", err)
			}

			if result.Status != payment.SubscriptionStatusActive {
				t.Errorf("Expected subscription status 'active', got '%s'", result.Status)
			}

			if result.CustomerID != "cus_test_123456789" {
				t.Errorf("Expected customer ID 'cus_test_123456789', got '%s'", result.CustomerID)
			}

			if result.PriceID != "plan_test_123456789" {
				t.Errorf("Expected plan ID 'plan_test_123456789', got '%s'", result.PriceID)
			}

			if result.ProviderID != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.ProviderID)
			}
		})

		// Test failed subscription
		t.Run("Failed Subscription", func(t *testing.T) {
			result, err := runner.RunSubscriptionTest(ctx, "cus_test_123456789", "plan_test_123456789", TestModeFailure)
			if err != nil {
				t.Fatalf("Subscription test failed: %v", err)
			}

			if result.Status != payment.SubscriptionStatusIncomplete {
				t.Errorf("Expected subscription status 'incomplete', got '%s'", result.Status)
			}

			// For incomplete subscriptions, additional handling may be needed
			// but no specific error fields are expected in the result structure
		})
	}
}

// testWebhookProcessing tests webhook processing scenarios
func testWebhookProcessing(plugin *StripePlugin, testUtils *TestingUtilities) func(t *testing.T) {
	return func(t *testing.T) {
		ctx := context.Background()
		runner := NewTestRunner(testUtils)

		// Test payment intent succeeded webhook
		t.Run("Payment Intent Succeeded", func(t *testing.T) {
			testData := testUtils.CreateTestPaymentIntent(10000, "usd") // $100.00
			result, err := runner.RunWebhookTest(ctx, "payment_intent.succeeded", testData)
			if err != nil {
				t.Fatalf("Webhook test failed: %v", err)
			}

			if result.Provider != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.Provider)
			}

			if len(result.Events) == 0 {
				t.Error("Expected at least one event result")
			}

			if result.Events[0].Event != "payment_intent.succeeded" {
				t.Errorf("Expected event 'payment_intent.succeeded', got '%s'", result.Events[0].Event)
			}
		})

		// Test subscription created webhook
		t.Run("Subscription Created", func(t *testing.T) {
			testData := testUtils.CreateTestSubscription("cus_test_123456789", "plan_test_123456789")
			result, err := runner.RunWebhookTest(ctx, "customer.subscription.created", testData)
			if err != nil {
				t.Fatalf("Webhook test failed: %v", err)
			}

			if result.Provider != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.Provider)
			}

			if len(result.Events) == 0 {
				t.Error("Expected at least one event result")
			}

			if result.Events[0].Event != "customer.subscription.created" {
				t.Errorf("Expected event 'customer.subscription.created', got '%s'", result.Events[0].Event)
			}
		})

		// Test invoice payment succeeded webhook
		t.Run("Invoice Payment Succeeded", func(t *testing.T) {
			testData := testUtils.CreateTestInvoice("sub_test_123456789", 2000) // $20.00
			result, err := runner.RunWebhookTest(ctx, "invoice.payment_succeeded", testData)
			if err != nil {
				t.Fatalf("Webhook test failed: %v", err)
			}

			if result.Provider != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.Provider)
			}

			if len(result.Events) == 0 {
				t.Error("Expected at least one event result")
			}

			if result.Events[0].Event != "invoice.payment_succeeded" {
				t.Errorf("Expected event 'invoice.payment_succeeded', got '%s'", result.Events[0].Event)
			}
		})

		// Test dispute created webhook
		t.Run("Dispute Created", func(t *testing.T) {
			testData := testUtils.CreateTestDispute("ch_test_123456789", 5000) // $50.00
			result, err := runner.RunWebhookTest(ctx, "charge.dispute.created", testData)
			if err != nil {
				t.Fatalf("Webhook test failed: %v", err)
			}

			if result.Provider != "stripe" {
				t.Errorf("Expected provider 'stripe', got '%s'", result.Provider)
			}

			if len(result.Events) == 0 {
				t.Error("Expected at least one event result")
			}

			if result.Events[0].Event != "charge.dispute.created" {
				t.Errorf("Expected event 'charge.dispute.created', got '%s'", result.Events[0].Event)
			}
		})
	}
}

// testErrorHandling tests error handling scenarios
func testErrorHandling(plugin *StripePlugin, testUtils *TestingUtilities) func(t *testing.T) {
	return func(t *testing.T) {
		ctx := context.Background()
		runner := NewTestRunner(testUtils)

		// Test processing error
		t.Run("Processing Error", func(t *testing.T) {
			result, err := runner.RunPaymentTest(ctx, 100.00, "USD", TestModeProcessingError)
			if err != nil {
				t.Fatalf("Payment test failed: %v", err)
			}

			if result.Status != payment.PaymentStatusFailed {
				t.Errorf("Expected payment status 'failed', got '%s'", result.Status)
			}

			if result.Metadata["error_code"] != "processing_error" {
				t.Errorf("Expected error code 'processing_error', got '%s'", result.Metadata["error_code"])
			}
		})

		// Test configuration validation
		t.Run("Configuration Validation", func(t *testing.T) {
			err := testUtils.ValidateTestConfiguration()
			if err != nil {
				t.Errorf("Test configuration validation failed: %v", err)
			}
		})

		// Test invalid test mode
		t.Run("Invalid Test Mode", func(t *testing.T) {
			testUtils.SetTestMode("invalid_mode")
			err := testUtils.ValidateTestConfiguration()
			if err == nil {
				t.Error("Expected error for invalid test mode")
			}
		})
	}
}

// BenchmarkPaymentProcessing benchmarks payment processing performance
func BenchmarkPaymentProcessing(b *testing.B) {
	plugin := createTestPlugin(nil)
	testUtils := NewTestingUtilities(plugin, &TestConfig{
		SandboxMode: true,
		TestMode:    TestModeSuccess,
	})
	runner := NewTestRunner(testUtils)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := runner.RunPaymentTest(ctx, 100.00, "USD", TestModeSuccess)
		if err != nil {
			b.Fatalf("Payment test failed: %v", err)
		}
	}
}

// BenchmarkRefundProcessing benchmarks refund processing performance
func BenchmarkRefundProcessing(b *testing.B) {
	plugin := createTestPlugin(nil)
	testUtils := NewTestingUtilities(plugin, &TestConfig{
		SandboxMode: true,
		TestMode:    TestModeSuccess,
	})
	runner := NewTestRunner(testUtils)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := runner.RunRefundTest(ctx, "pi_test_123456789", 100.00, TestModeSuccess)
		if err != nil {
			b.Fatalf("Refund test failed: %v", err)
		}
	}
}

// BenchmarkWebhookProcessing benchmarks webhook processing performance
func BenchmarkWebhookProcessing(b *testing.B) {
	plugin := createTestPlugin(nil)
	testUtils := NewTestingUtilities(plugin, &TestConfig{
		SandboxMode: true,
		TestMode:    TestModeSuccess,
	})
	runner := NewTestRunner(testUtils)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testData := testUtils.CreateTestPaymentIntent(10000, "usd")
		_, err := runner.RunWebhookTest(ctx, "payment_intent.succeeded", testData)
		if err != nil {
			b.Fatalf("Webhook test failed: %v", err)
		}
	}
}

// TestConcurrentPayments tests concurrent payment processing
func TestConcurrentPayments(t *testing.T) {
	plugin := createTestPlugin(t)
	testUtils := NewTestingUtilities(plugin, &TestConfig{
		SandboxMode: true,
		TestMode:    TestModeSuccess,
	})
	runner := NewTestRunner(testUtils)
	ctx := context.Background()

	const numConcurrentPayments = 10
	results := make(chan *payment.PaymentResult, numConcurrentPayments)
	errors := make(chan error, numConcurrentPayments)

	// Start concurrent payments
	for i := 0; i < numConcurrentPayments; i++ {
		go func(i int) {
			result, err := runner.RunPaymentTest(ctx, 100.00, "USD", TestModeSuccess)
			if err != nil {
				errors <- err
				return
			}
			results <- result
		}(i)
	}

	// Collect results
	successCount := 0
	errorCount := 0

	for i := 0; i < numConcurrentPayments; i++ {
		select {
		case result := <-results:
			if result.Status == payment.PaymentStatusSucceeded {
				successCount++
			}
		case err := <-errors:
			t.Errorf("Concurrent payment failed: %v", err)
			errorCount++
		case <-time.After(5 * time.Second):
			t.Error("Timeout waiting for concurrent payment results")
			return
		}
	}

	if successCount != numConcurrentPayments {
		t.Errorf("Expected %d successful payments, got %d", numConcurrentPayments, successCount)
	}

	if errorCount > 0 {
		t.Errorf("Expected 0 errors, got %d", errorCount)
	}
}

// TestTestUtilities tests the testing utilities themselves
func TestTestUtilities(t *testing.T) {
	plugin := createTestPlugin(t)
	testUtils := NewTestingUtilities(plugin, nil)

	// Test default configuration
	if !testUtils.IsSandboxMode() {
		t.Error("Expected sandbox mode to be enabled by default")
	}

	// Test mode switching
	testUtils.SetTestMode(TestModeFailure)
	if testUtils.config.TestMode != TestModeFailure {
		t.Errorf("Expected test mode '%s', got '%s'", TestModeFailure, testUtils.config.TestMode)
	}

	// Test sandbox mode toggle
	testUtils.DisableSandboxMode()
	if testUtils.IsSandboxMode() {
		t.Error("Expected sandbox mode to be disabled")
	}

	testUtils.EnableSandboxMode()
	if !testUtils.IsSandboxMode() {
		t.Error("Expected sandbox mode to be enabled")
	}

	// Test delay configuration
	delay := 100 * time.Millisecond
	testUtils.SetDelayResponses(delay)
	if testUtils.config.DelayResponses != delay {
		t.Errorf("Expected delay %v, got %v", delay, testUtils.config.DelayResponses)
	}

	// Test test card numbers
	for mode, expectedCard := range TestCardNumbers {
		card := testUtils.GetTestCardNumber(mode)
		if card != expectedCard {
			t.Errorf("Expected card number '%s' for mode '%s', got '%s'", expectedCard, mode, card)
		}
	}

	// Test invalid mode fallback
	invalidModeCard := testUtils.GetTestCardNumber("invalid_mode")
	if invalidModeCard != TestCardNumbers[TestModeSuccess] {
		t.Errorf("Expected fallback to success card, got '%s'", invalidModeCard)
	}
}
