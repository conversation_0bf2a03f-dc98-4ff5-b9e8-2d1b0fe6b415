package stripe

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stripe/stripe-go/v76"
	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// TestMode constants for different testing scenarios
const (
	TestModeSuccess           = "success"
	TestModeFailure           = "failure"
	TestModeRequiresAction    = "requires_action"
	TestModeDeclined          = "declined"
	TestModeInsufficientFunds = "insufficient_funds"
	TestModeExpiredCard       = "expired_card"
	TestModeProcessingError   = "processing_error"
)

// TestCardNumbers for different test scenarios
var TestCardNumbers = map[string]string{
	TestModeSuccess:           "****************", // Visa
	TestModeFailure:           "****************", // Generic decline
	TestModeRequiresAction:    "****************", // Requires 3DS authentication
	TestModeDeclined:          "****************", // Generic decline
	TestModeInsufficientFunds: "****************", // Insufficient funds
	TestModeExpiredCard:       "****************", // Expired card
	TestModeProcessingError:   "****************", // Processing error
}

// TestingUtilities provides utilities for testing Stripe payment plugin
type TestingUtilities struct {
	plugin *StripePlugin
	config *TestConfig
}

// TestConfig holds configuration for testing
type TestConfig struct {
	SandboxMode    bool
	TestMode       string
	MockResponses  map[string]interface{}
	DelayResponses time.Duration
}

// NewTestingUtilities creates a new testing utilities instance
func NewTestingUtilities(plugin *StripePlugin, config *TestConfig) *TestingUtilities {
	if config == nil {
		config = &TestConfig{
			SandboxMode:   true,
			TestMode:      TestModeSuccess,
			MockResponses: make(map[string]interface{}),
		}
	}
	return &TestingUtilities{
		plugin: plugin,
		config: config,
	}
}

// MockPaymentResult creates a mock payment result for testing
func (tu *TestingUtilities) MockPaymentResult(request *payment.PaymentRequest) *payment.PaymentResult {
	// Simulate processing delay
	if tu.config.DelayResponses > 0 {
		time.Sleep(tu.config.DelayResponses)
	}

	paymentID := tu.generateTestPaymentID()

	switch tu.config.TestMode {
	case TestModeSuccess:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusSucceeded,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":      true,
				"test_scenario":  TestModeSuccess,
				"card_last_four": "4242",
				"card_brand":     "visa",
				"payment_method": "card",
				"transaction_id": tu.generateTestTransactionID(),
				"created_at":     time.Now().Unix(),
			},
		}
	case TestModeFailure, TestModeDeclined:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusFailed,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":      true,
				"test_scenario":  tu.config.TestMode,
				"decline_code":   "generic_decline",
				"failure_reason": "card_declined",
				"error_code":     "card_declined",
				"error_message":  "Your card was declined.",
			},
		}
	case TestModeRequiresAction:
		return &payment.PaymentResult{
			PaymentID:    paymentID,
			Status:       payment.PaymentStatusRequiresAction,
			Amount:       request.Amount,
			Currency:     request.Currency,
			ProviderID:   "stripe",
			ClientSecret: tu.generateTestClientSecret(),
			Metadata: map[string]interface{}{
				"test_mode":       true,
				"test_scenario":   TestModeRequiresAction,
				"requires_action": true,
				"next_action":     "authenticate_3ds",
			},
		}
	case TestModeInsufficientFunds:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusFailed,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":     true,
				"test_scenario": TestModeInsufficientFunds,
				"decline_code":  "insufficient_funds",
				"error_code":    "insufficient_funds",
				"error_message": "Your card has insufficient funds.",
			},
		}
	case TestModeExpiredCard:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusFailed,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":     true,
				"test_scenario": TestModeExpiredCard,
				"decline_code":  "expired_card",
				"error_code":    "expired_card",
				"error_message": "Your card has expired.",
			},
		}
	case TestModeProcessingError:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusFailed,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":        true,
				"test_scenario":    TestModeProcessingError,
				"processing_error": true,
				"error_code":       "processing_error",
				"error_message":    "An error occurred while processing your payment.",
			},
		}
	default:
		return &payment.PaymentResult{
			PaymentID:  paymentID,
			Status:     payment.PaymentStatusSucceeded,
			Amount:     request.Amount,
			Currency:   request.Currency,
			ProviderID: "stripe",
			Metadata: map[string]interface{}{
				"test_mode":      true,
				"test_scenario":  "default",
				"transaction_id": tu.generateTestTransactionID(),
			},
		}
	}
}

// MockRefundResult creates a mock refund result for testing
func (tu *TestingUtilities) MockRefundResult(request *payment.RefundRequest) *payment.RefundResult {
	if tu.config.DelayResponses > 0 {
		time.Sleep(tu.config.DelayResponses)
	}

	refundID := tu.generateTestRefundID()

	// Handle optional amount - if not provided, use full amount
	amount := decimal.NewFromFloat(100.00) // Default amount
	if request.Amount != nil {
		amount = *request.Amount
	}

	switch tu.config.TestMode {
	case TestModeSuccess:
		return &payment.RefundResult{
			RefundID:  refundID,
			PaymentID: request.PaymentID,
			Status:    payment.RefundStatusSucceeded,
			Amount:    amount,
			Currency:  "USD",
			Reason:    request.Reason,
			Metadata: map[string]interface{}{
				"test_mode":        true,
				"test_scenario":    TestModeSuccess,
				"refund_reason":    request.Reason,
				"original_payment": request.PaymentID,
				"created_at":       time.Now().Unix(),
			},
		}
	case TestModeFailure:
		return &payment.RefundResult{
			RefundID:  refundID,
			PaymentID: request.PaymentID,
			Status:    payment.RefundStatusFailed,
			Amount:    amount,
			Currency:  "USD",
			Reason:    request.Reason,
			Metadata: map[string]interface{}{
				"test_mode":      true,
				"test_scenario":  TestModeFailure,
				"failure_reason": "refund_failed",
				"error_code":     "refund_failed",
				"error_message":  "The refund could not be processed.",
			},
		}
	default:
		return &payment.RefundResult{
			RefundID:  refundID,
			PaymentID: request.PaymentID,
			Status:    payment.RefundStatusSucceeded,
			Amount:    amount,
			Currency:  "USD",
			Reason:    request.Reason,
			Metadata: map[string]interface{}{
				"test_mode":     true,
				"test_scenario": "default",
			},
		}
	}
}

// MockSubscriptionResult creates a mock subscription result for testing
func (tu *TestingUtilities) MockSubscriptionResult(request *payment.SubscriptionRequest) *payment.SubscriptionResult {
	if tu.config.DelayResponses > 0 {
		time.Sleep(tu.config.DelayResponses)
	}

	subscriptionID := tu.generateTestSubscriptionID()
	now := time.Now()

	switch tu.config.TestMode {
	case TestModeSuccess:
		return &payment.SubscriptionResult{
			SubscriptionID:     subscriptionID,
			Status:             payment.SubscriptionStatusActive,
			CustomerID:         request.CustomerID,
			PriceID:            request.PriceID,
			ProviderID:         "stripe",
			CurrentPeriodStart: &now,
			CurrentPeriodEnd:   timePtr(now.AddDate(0, 1, 0)),
			CreatedAt:          &now,
		}
	case TestModeFailure:
		return &payment.SubscriptionResult{
			SubscriptionID: subscriptionID,
			Status:         payment.SubscriptionStatusIncomplete,
			CustomerID:     request.CustomerID,
			PriceID:        request.PriceID,
			ProviderID:     "stripe",
			CreatedAt:      &now,
		}
	default:
		return &payment.SubscriptionResult{
			SubscriptionID:     subscriptionID,
			Status:             payment.SubscriptionStatusActive,
			CustomerID:         request.CustomerID,
			PriceID:            request.PriceID,
			ProviderID:         "stripe",
			CurrentPeriodStart: &now,
			CurrentPeriodEnd:   timePtr(now.AddDate(0, 1, 0)),
			CreatedAt:          &now,
		}
	}
}

// MockWebhookEvent creates a mock webhook event for testing
func (tu *TestingUtilities) MockWebhookEvent(eventType string, objectData interface{}) *stripe.Event {
	eventID := tu.generateTestEventID()

	rawData, _ := json.Marshal(objectData)

	return &stripe.Event{
		ID:      eventID,
		Type:    stripe.EventType(eventType),
		Created: time.Now().Unix(),
		Data: &stripe.EventData{
			Raw: rawData,
		},
		Livemode: false, // Always false for test events
	}
}

// CreateTestPaymentIntent creates a test payment intent object
func (tu *TestingUtilities) CreateTestPaymentIntent(amount int64, currency string) *stripe.PaymentIntent {
	return &stripe.PaymentIntent{
		ID:                 tu.generateTestPaymentID(),
		Amount:             amount,
		Currency:           stripe.Currency(currency),
		Status:             stripe.PaymentIntentStatusSucceeded,
		ConfirmationMethod: stripe.PaymentIntentConfirmationMethodAutomatic,
		PaymentMethod: &stripe.PaymentMethod{
			ID:   tu.generateTestPaymentMethodID(),
			Type: stripe.PaymentMethodTypeCard,
		},
		ClientSecret: tu.generateTestClientSecret(),
		Created:      time.Now().Unix(),
		Metadata: map[string]string{
			"test_mode": "true",
		},
	}
}

// CreateTestSubscription creates a test subscription object
func (tu *TestingUtilities) CreateTestSubscription(customerID, planID string) *stripe.Subscription {
	now := time.Now()
	return &stripe.Subscription{
		ID:     tu.generateTestSubscriptionID(),
		Status: stripe.SubscriptionStatusActive,
		Customer: &stripe.Customer{
			ID: customerID,
		},
		CurrentPeriodStart: now.Unix(),
		CurrentPeriodEnd:   now.AddDate(0, 1, 0).Unix(),
		Created:            now.Unix(),
		Metadata: map[string]string{
			"test_mode": "true",
			"plan_id":   planID,
		},
	}
}

// CreateTestInvoice creates a test invoice object
func (tu *TestingUtilities) CreateTestInvoice(subscriptionID string, amount int64) *stripe.Invoice {
	return &stripe.Invoice{
		ID:         tu.generateTestInvoiceID(),
		AmountPaid: amount,
		AmountDue:  0,
		Currency:   stripe.CurrencyUSD,
		Status:     stripe.InvoiceStatusPaid,
		Subscription: &stripe.Subscription{
			ID: subscriptionID,
		},
		PaymentIntent: &stripe.PaymentIntent{
			ID: tu.generateTestPaymentID(),
		},
		BillingReason: stripe.InvoiceBillingReasonSubscriptionCycle,
		Created:       time.Now().Unix(),
		Metadata: map[string]string{
			"test_mode": "true",
		},
	}
}

// CreateTestDispute creates a test dispute object
func (tu *TestingUtilities) CreateTestDispute(chargeID string, amount int64) *stripe.Dispute {
	return &stripe.Dispute{
		ID:       tu.generateTestDisputeID(),
		Amount:   amount,
		Currency: stripe.CurrencyUSD,
		Reason:   stripe.DisputeReasonFraudulent,
		Status:   stripe.DisputeStatusWarningNeedsResponse,
		Charge: &stripe.Charge{
			ID: chargeID,
			PaymentIntent: &stripe.PaymentIntent{
				ID: tu.generateTestPaymentID(),
			},
		},
		Created: time.Now().Unix(),
		Metadata: map[string]string{
			"test_mode": "true",
		},
	}
}

// SetTestMode sets the test mode for the utilities
func (tu *TestingUtilities) SetTestMode(mode string) {
	tu.config.TestMode = mode
}

// SetDelayResponses sets artificial delay for responses
func (tu *TestingUtilities) SetDelayResponses(delay time.Duration) {
	tu.config.DelayResponses = delay
}

// EnableSandboxMode enables sandbox mode
func (tu *TestingUtilities) EnableSandboxMode() {
	tu.config.SandboxMode = true
}

// DisableSandboxMode disables sandbox mode
func (tu *TestingUtilities) DisableSandboxMode() {
	tu.config.SandboxMode = false
}

// IsSandboxMode returns true if sandbox mode is enabled
func (tu *TestingUtilities) IsSandboxMode() bool {
	return tu.config.SandboxMode
}

// GetTestCardNumber returns the test card number for a given test mode
func (tu *TestingUtilities) GetTestCardNumber(mode string) string {
	if cardNumber, exists := TestCardNumbers[mode]; exists {
		return cardNumber
	}
	return TestCardNumbers[TestModeSuccess] // Default to success card
}

// ValidateTestConfiguration validates the test configuration
func (tu *TestingUtilities) ValidateTestConfiguration() error {
	if tu.config == nil {
		return fmt.Errorf("test configuration is nil")
	}

	if tu.config.TestMode == "" {
		return fmt.Errorf("test mode is not set")
	}

	validModes := []string{
		TestModeSuccess,
		TestModeFailure,
		TestModeRequiresAction,
		TestModeDeclined,
		TestModeInsufficientFunds,
		TestModeExpiredCard,
		TestModeProcessingError,
	}

	for _, mode := range validModes {
		if tu.config.TestMode == mode {
			return nil
		}
	}

	return fmt.Errorf("invalid test mode: %s", tu.config.TestMode)
}

// Helper functions for generating test IDs
func (tu *TestingUtilities) generateTestPaymentID() string {
	return fmt.Sprintf("pi_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestTransactionID() string {
	return fmt.Sprintf("txn_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestRefundID() string {
	return fmt.Sprintf("re_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestSubscriptionID() string {
	return fmt.Sprintf("sub_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestEventID() string {
	return fmt.Sprintf("evt_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestPaymentMethodID() string {
	return fmt.Sprintf("pm_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestClientSecret() string {
	return fmt.Sprintf("pi_test_%s_secret_%s", tu.generateRandomString(24), tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestInvoiceID() string {
	return fmt.Sprintf("in_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateTestDisputeID() string {
	return fmt.Sprintf("dp_test_%s", tu.generateRandomString(24))
}

func (tu *TestingUtilities) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// TestRunner provides methods to run various tests
type TestRunner struct {
	utilities *TestingUtilities
}

// NewTestRunner creates a new test runner
func NewTestRunner(utilities *TestingUtilities) *TestRunner {
	return &TestRunner{
		utilities: utilities,
	}
}

// RunPaymentTest runs a payment test with the given parameters
func (tr *TestRunner) RunPaymentTest(ctx context.Context, amount float64, currency string, testMode string) (*payment.PaymentResult, error) {
	tr.utilities.SetTestMode(testMode)

	request := &payment.PaymentRequest{
		Amount:        decimal.NewFromFloat(amount),
		Currency:      currency,
		Description:   fmt.Sprintf("Test payment - %s", testMode),
		CustomerEmail: "<EMAIL>",
		Metadata: map[string]interface{}{
			"test_mode":     true,
			"test_scenario": testMode,
			"card_number":   tr.utilities.GetTestCardNumber(testMode),
		},
	}

	return tr.utilities.MockPaymentResult(request), nil
}

// RunRefundTest runs a refund test
func (tr *TestRunner) RunRefundTest(ctx context.Context, paymentID string, amount float64, testMode string) (*payment.RefundResult, error) {
	tr.utilities.SetTestMode(testMode)

	refundAmount := decimal.NewFromFloat(amount)
	request := &payment.RefundRequest{
		PaymentID: paymentID,
		Amount:    &refundAmount,
		Reason:    "Test refund",
		Metadata: map[string]interface{}{
			"test_mode":     true,
			"test_scenario": testMode,
		},
	}

	return tr.utilities.MockRefundResult(request), nil
}

// RunSubscriptionTest runs a subscription test
func (tr *TestRunner) RunSubscriptionTest(ctx context.Context, customerID, planID string, testMode string) (*payment.SubscriptionResult, error) {
	tr.utilities.SetTestMode(testMode)

	request := &payment.SubscriptionRequest{
		CustomerID:    customerID,
		CustomerEmail: "<EMAIL>",
		CustomerName:  "Test User",
		PriceID:       planID,
		Metadata: map[string]interface{}{
			"test_mode":     true,
			"test_scenario": testMode,
			"card_number":   tr.utilities.GetTestCardNumber(testMode),
		},
	}

	return tr.utilities.MockSubscriptionResult(request), nil
}

// RunWebhookTest runs a webhook test
func (tr *TestRunner) RunWebhookTest(ctx context.Context, eventType string, testData interface{}) (*payment.WebhookResult, error) {
	event := tr.utilities.MockWebhookEvent(eventType, testData)

	// Mock webhook handling
	return &payment.WebhookResult{
		Provider: "stripe",
		Events: []*payment.WebhookEventResult{
			{
				Event:     eventType,
				Timestamp: time.Now(),
				Metadata: map[string]interface{}{
					"test_mode":       true,
					"stripe_event_id": event.ID,
					"event_type":      eventType,
				},
			},
		},
	}, nil
}
