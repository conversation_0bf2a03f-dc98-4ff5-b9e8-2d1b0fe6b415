package stripe

import (
	"github.com/tranthanhloi/wn-api-v3/pkg/plugin"
	"github.com/tranthanhloi/wn-api-v3/plugins/payment"
)

// init registers the Stripe plugin
func init() {
	// Register the Stripe plugin factory
	plugin.RegisterFactory("stripe", NewStripePluginAdapter)
}

// NewStripePluginAdapter creates a new Stripe plugin adapter instance
func NewStripePluginAdapter(config map[string]interface{}) (plugin.Plugin, error) {
	// Create the payment plugin
	paymentPlugin, err := NewStripePlugin(config, nil)
	if err != nil {
		return nil, err
	}

	// Get plugin info
	pluginInfo := StripePluginInfo()

	// Create plugin adapter
	return payment.NewPaymentPluginAdapter(pluginInfo, paymentPlugin, nil), nil
}

// RegisterStripePlugin registers the Stripe plugin with a custom logger
func RegisterStripePlugin(logger plugin.Logger) error {
	factory := func(config map[string]interface{}) (plugin.Plugin, error) {
		// Create the payment plugin
		paymentPlugin, err := NewStripePlugin(config, logger)
		if err != nil {
			return nil, err
		}

		// Get plugin info
		pluginInfo := StripePluginInfo()

		// Create plugin adapter
		return payment.NewPaymentPluginAdapter(pluginInfo, paymentPlugin, logger), nil
	}

	return plugin.RegisterFactory("stripe", factory)
}
