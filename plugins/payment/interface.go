package payment

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

// PaymentPlugin defines the interface for payment gateway plugins
type PaymentPlugin interface {
	// ProcessPayment processes a one-time payment
	ProcessPayment(ctx context.Context, request *PaymentRequest) (*PaymentResult, error)

	// RefundPayment refunds a processed payment
	RefundPayment(ctx context.Context, request *RefundRequest) (*RefundResult, error)

	// CreateSubscription creates a recurring subscription
	CreateSubscription(ctx context.Context, request *SubscriptionRequest) (*SubscriptionResult, error)

	// UpdateSubscription updates an existing subscription
	UpdateSubscription(ctx context.Context, request *SubscriptionUpdateRequest) (*SubscriptionResult, error)

	// CancelSubscription cancels a subscription
	CancelSubscription(ctx context.Context, subscriptionID string) error

	// GetPaymentStatus retrieves the current status of a payment
	GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error)

	// GetSubscriptionStatus retrieves the current status of a subscription
	GetSubscriptionStatus(ctx context.Context, subscriptionID string) (*SubscriptionStatus, error)

	// HandleWebhook processes webhook events from the payment provider
	HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)

	// GetSupportedCurrencies returns the list of supported currencies
	GetSupportedCurrencies() []string

	// GetSupportedPaymentMethods returns the list of supported payment methods
	GetSupportedPaymentMethods() []string

	// ValidateConfiguration validates the plugin configuration
	ValidateConfiguration(config map[string]interface{}) error
}

// PaymentRequest represents a payment request
type PaymentRequest struct {
	Amount          decimal.Decimal        `json:"amount"`
	Currency        string                 `json:"currency"`
	Description     string                 `json:"description"`
	CustomerID      string                 `json:"customer_id,omitempty"`
	CustomerEmail   string                 `json:"customer_email,omitempty"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	ReturnURL       string                 `json:"return_url,omitempty"`
	CancelURL       string                 `json:"cancel_url,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentResult represents a payment processing result
type PaymentResult struct {
	PaymentID    string                 `json:"payment_id"`
	Status       PaymentStatusType      `json:"status"`
	Amount       decimal.Decimal        `json:"amount"`
	Currency     string                 `json:"currency"`
	ProviderID   string                 `json:"provider_id"`
	ClientSecret string                 `json:"client_secret,omitempty"`
	RedirectURL  string                 `json:"redirect_url,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt    *time.Time             `json:"created_at,omitempty"`
}

// RefundRequest represents a refund request
type RefundRequest struct {
	PaymentID string                 `json:"payment_id"`
	Amount    *decimal.Decimal       `json:"amount,omitempty"` // Optional for partial refunds
	Reason    string                 `json:"reason,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// RefundResult represents a refund processing result
type RefundResult struct {
	RefundID  string                 `json:"refund_id"`
	PaymentID string                 `json:"payment_id"`
	Status    RefundStatusType       `json:"status"`
	Amount    decimal.Decimal        `json:"amount"`
	Currency  string                 `json:"currency"`
	Reason    string                 `json:"reason,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt *time.Time             `json:"created_at,omitempty"`
}

// SubscriptionRequest represents a subscription creation request
type SubscriptionRequest struct {
	CustomerID      string                 `json:"customer_id,omitempty"`
	CustomerEmail   string                 `json:"customer_email"`
	CustomerName    string                 `json:"customer_name,omitempty"`
	PriceID         string                 `json:"price_id"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	TrialDays       int                    `json:"trial_days,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionUpdateRequest represents a subscription update request
type SubscriptionUpdateRequest struct {
	SubscriptionID  string                 `json:"subscription_id"`
	PriceID         string                 `json:"price_id,omitempty"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionResult represents a subscription operation result
type SubscriptionResult struct {
	SubscriptionID     string                 `json:"subscription_id"`
	Status             SubscriptionStatusType `json:"status"`
	CustomerID         string                 `json:"customer_id"`
	PriceID            string                 `json:"price_id"`
	CurrentPeriodStart *time.Time             `json:"current_period_start,omitempty"`
	CurrentPeriodEnd   *time.Time             `json:"current_period_end,omitempty"`
	TrialEnd           *time.Time             `json:"trial_end,omitempty"`
	ProviderID         string                 `json:"provider_id"`
	CreatedAt          *time.Time             `json:"created_at,omitempty"`
}

// PaymentStatus represents the current status of a payment
type PaymentStatus struct {
	PaymentID string                 `json:"payment_id"`
	Status    PaymentStatusType      `json:"status"`
	Amount    decimal.Decimal        `json:"amount"`
	Currency  string                 `json:"currency"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	UpdatedAt *time.Time             `json:"updated_at,omitempty"`
}

// SubscriptionStatus represents the current status of a subscription
type SubscriptionStatus struct {
	SubscriptionID     string                 `json:"subscription_id"`
	Status             SubscriptionStatusType `json:"status"`
	CurrentPeriodStart *time.Time             `json:"current_period_start,omitempty"`
	CurrentPeriodEnd   *time.Time             `json:"current_period_end,omitempty"`
	UpdatedAt          *time.Time             `json:"updated_at,omitempty"`
}

// WebhookResult represents the result of webhook processing
type WebhookResult struct {
	Provider string                `json:"provider"`
	Events   []*WebhookEventResult `json:"events"`
}

// WebhookEventResult represents a single webhook event result
type WebhookEventResult struct {
	PaymentID      string                 `json:"payment_id,omitempty"`
	SubscriptionID string                 `json:"subscription_id,omitempty"`
	Event          string                 `json:"event"`
	Timestamp      time.Time              `json:"timestamp"`
	Status         PaymentStatusType      `json:"status,omitempty"`
	Amount         decimal.Decimal        `json:"amount,omitempty"`
	Currency       string                 `json:"currency,omitempty"`
	Reason         string                 `json:"reason,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentStatusType represents payment status types
type PaymentStatusType string

const (
	PaymentStatusPending        PaymentStatusType = "pending"
	PaymentStatusProcessing     PaymentStatusType = "processing"
	PaymentStatusSucceeded      PaymentStatusType = "succeeded"
	PaymentStatusFailed         PaymentStatusType = "failed"
	PaymentStatusCanceled       PaymentStatusType = "canceled"
	PaymentStatusRequiresAction PaymentStatusType = "requires_action"
)

// RefundStatusType represents refund status types
type RefundStatusType string

const (
	RefundStatusPending   RefundStatusType = "pending"
	RefundStatusSucceeded RefundStatusType = "succeeded"
	RefundStatusFailed    RefundStatusType = "failed"
	RefundStatusCanceled  RefundStatusType = "canceled"
)

// SubscriptionStatusType represents subscription status types
type SubscriptionStatusType string

const (
	SubscriptionStatusActive     SubscriptionStatusType = "active"
	SubscriptionStatusTrialing   SubscriptionStatusType = "trialing"
	SubscriptionStatusPastDue    SubscriptionStatusType = "past_due"
	SubscriptionStatusCanceled   SubscriptionStatusType = "canceled"
	SubscriptionStatusUnpaid     SubscriptionStatusType = "unpaid"
	SubscriptionStatusIncomplete SubscriptionStatusType = "incomplete"
)

// PaymentMethod represents a payment method
type PaymentMethod struct {
	ID          string                 `json:"id"`
	Type        PaymentMethodType      `json:"type"`
	Last4       string                 `json:"last4,omitempty"`
	Brand       string                 `json:"brand,omitempty"`
	ExpiryMonth int                    `json:"expiry_month,omitempty"`
	ExpiryYear  int                    `json:"expiry_year,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentMethodType represents payment method types
type PaymentMethodType string

const (
	PaymentMethodCard        PaymentMethodType = "card"
	PaymentMethodBankAccount PaymentMethodType = "bank_account"
	PaymentMethodWallet      PaymentMethodType = "wallet"
	PaymentMethodSepaDebit   PaymentMethodType = "sepa_debit"
	PaymentMethodIdeal       PaymentMethodType = "ideal"
	PaymentMethodSofort      PaymentMethodType = "sofort"
)

// PaymentError represents a payment-specific error
type PaymentError struct {
	Code    string
	Message string
	Details map[string]interface{}
}

func (e *PaymentError) Error() string {
	return e.Message
}

// NewPaymentError creates a new payment error
func NewPaymentError(code, message string) *PaymentError {
	return &PaymentError{
		Code:    code,
		Message: message,
		Details: make(map[string]interface{}),
	}
}

// Common payment error codes
const (
	ErrorCodeInvalidCard       = "invalid_card"
	ErrorCodeCardDeclined      = "card_declined"
	ErrorCodeInsufficientFunds = "insufficient_funds"
	ErrorCodeExpiredCard       = "expired_card"
	ErrorCodeInvalidAPIKey     = "invalid_api_key"
	ErrorCodePaymentFailed     = "payment_failed"
	ErrorCodeRefundFailed      = "refund_failed"
	ErrorCodeWebhookFailed     = "webhook_failed"
	ErrorCodeInvalidCurrency   = "invalid_currency"
	ErrorCodeInvalidAmount     = "invalid_amount"
)
