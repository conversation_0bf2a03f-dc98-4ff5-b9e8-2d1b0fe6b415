package validator

import (
	"context"
)

// Validator interface defines the contract for validation
type Validator interface {
	// Validate validates a struct and returns validation errors
	Validate(ctx context.Context, v interface{}) error

	// ValidateField validates a single field
	ValidateField(ctx context.Context, field interface{}, tag string) error

	// RegisterValidation registers a custom validation function
	RegisterValidation(tag string, fn ValidationFunc) error

	// RegisterTagNameFunc registers a function to get alternate names for struct fields
	RegisterTagNameFunc(fn TagNameFunc)

	// RegisterTranslation registers a translation for a validation tag
	RegisterTranslation(tag string, locale string, translation string) error

	// SetLocale sets the locale for error messages
	SetLocale(locale string) error
}

// ValidationFunc is the signature for custom validation functions
type ValidationFunc func(ctx context.Context, fl FieldLevel) bool

// FieldLevel contains all the information and helper methods for custom validation
type FieldLevel interface {
	// Field returns the current field value
	Field() interface{}

	// FieldName returns the field name
	FieldName() string

	// StructFieldName returns the struct field name
	StructFieldName() string

	// Param returns the parameter for the validation tag
	Param() string

	// GetTag returns the tag name
	GetTag() string

	// ExtractType gets the underlying type information
	ExtractType() (interface{}, bool)
}

// TagNameFunc is the signature for tag name functions
type TagNameFunc func(field interface{}) string

// ValidationError represents a validation error
type ValidationError interface {
	error

	// Field returns the field that failed validation
	Field() string

	// Tag returns the validation tag that failed
	Tag() string

	// Value returns the value that failed validation
	Value() interface{}

	// Param returns the parameter for the validation tag
	Param() string

	// Message returns the error message
	Message() string

	// Translate returns the translated error message
	Translate(translator Translator) string
}

// ValidationErrors is a slice of ValidationError
type ValidationErrors []ValidationError

// Error returns the error message
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return ""
	}
	return ve[0].Error()
}

// Translator interface for translating validation errors
type Translator interface {
	// Translate translates a key with parameters
	Translate(key string, params map[string]string) string

	// Locale returns the current locale
	Locale() string
}

// Options for creating a new validator
type Options struct {
	// TagName is the struct tag name for validation rules
	TagName string

	// DefaultLocale is the default locale for error messages
	DefaultLocale string

	// RequiredDefault sets whether fields are required by default
	RequiredDefault bool

	// CustomTranslations for validation messages
	CustomTranslations map[string]map[string]string
}
