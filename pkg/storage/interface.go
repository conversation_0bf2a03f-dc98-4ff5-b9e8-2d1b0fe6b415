package storage

import (
	"context"
	"io"
	"time"
)

// Storage defines interface for file storage operations
type Storage interface {
	// File operations
	Put(ctx context.Context, key string, reader io.Reader, options *PutOptions) error
	Get(ctx context.Context, key string) (io.ReadCloser, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)

	// File info operations
	GetInfo(ctx context.Context, key string) (*FileInfo, error)
	GetURL(ctx context.Context, key string, expiry time.Duration) (string, error)
	GetPublicURL(ctx context.Context, key string) (string, error)

	// List operations
	List(ctx context.Context, prefix string, options *ListOptions) (*ListResult, error)
	ListAll(ctx context.Context, prefix string) ([]*FileInfo, error)

	// Batch operations
	PutMultiple(ctx context.Context, files []*FileUpload) error
	DeleteMultiple(ctx context.Context, keys []string) error

	// Copy and move operations
	Copy(ctx context.Context, src<PERSON><PERSON>, dst<PERSON>ey string) error
	Move(ctx context.Context, srcKey, dstKey string) error

	// Connection management
	Connect() error
	Disconnect() error
	Ping(ctx context.Context) error
}

// MediaStorage defines interface for media-specific operations
type MediaStorage interface {
	Storage

	// Media processing
	ProcessImage(ctx context.Context, key string, transformations *ImageTransformations) (string, error)
	GenerateThumbnail(ctx context.Context, key string, width, height int) (string, error)
	GetImageInfo(ctx context.Context, key string) (*ImageInfo, error)

	// Media variants
	CreateVariant(ctx context.Context, originalKey string, variant *MediaVariant) (string, error)
	GetVariants(ctx context.Context, originalKey string) ([]*MediaVariant, error)
	DeleteVariants(ctx context.Context, originalKey string) error

	// Media optimization
	OptimizeImage(ctx context.Context, key string, options *OptimizeOptions) (string, error)
	CompressVideo(ctx context.Context, key string, options *VideoOptions) (string, error)
}

// BucketStorage defines interface for bucket-based storage (S3, MinIO)
type BucketStorage interface {
	Storage

	// Bucket operations
	CreateBucket(ctx context.Context, bucket string) error
	DeleteBucket(ctx context.Context, bucket string) error
	BucketExists(ctx context.Context, bucket string) (bool, error)
	ListBuckets(ctx context.Context) ([]string, error)

	// Bucket policies
	SetBucketPolicy(ctx context.Context, bucket string, policy *BucketPolicy) error
	GetBucketPolicy(ctx context.Context, bucket string) (*BucketPolicy, error)
	DeleteBucketPolicy(ctx context.Context, bucket string) error

	// Bucket versioning
	SetBucketVersioning(ctx context.Context, bucket string, enabled bool) error
	GetBucketVersioning(ctx context.Context, bucket string) (bool, error)

	// Multipart uploads
	CreateMultipartUpload(ctx context.Context, key string, options *PutOptions) (*MultipartUpload, error)
	UploadPart(ctx context.Context, upload *MultipartUpload, partNumber int, reader io.Reader) (*UploadPart, error)
	CompleteMultipartUpload(ctx context.Context, upload *MultipartUpload, parts []*UploadPart) error
	AbortMultipartUpload(ctx context.Context, upload *MultipartUpload) error
}

// FileInfo represents file metadata
type FileInfo struct {
	Key          string            `json:"key"`
	Size         int64             `json:"size"`
	ContentType  string            `json:"content_type"`
	ETag         string            `json:"etag"`
	LastModified time.Time         `json:"last_modified"`
	Metadata     map[string]string `json:"metadata"`
	StorageClass string            `json:"storage_class,omitempty"`
	ACL          string            `json:"acl,omitempty"`
	IsPublic     bool              `json:"is_public"`
	URL          string            `json:"url,omitempty"`
	PublicURL    string            `json:"public_url,omitempty"`
}

// PutOptions represents options for file upload
type PutOptions struct {
	ContentType          string            `json:"content_type,omitempty"`
	ContentLength        int64             `json:"content_length,omitempty"`
	Metadata             map[string]string `json:"metadata,omitempty"`
	StorageClass         string            `json:"storage_class,omitempty"`
	ACL                  string            `json:"acl,omitempty"`
	CacheControl         string            `json:"cache_control,omitempty"`
	ContentEncoding      string            `json:"content_encoding,omitempty"`
	Expires              *time.Time        `json:"expires,omitempty"`
	ServerSideEncryption string            `json:"server_side_encryption,omitempty"`
	ChecksumSHA256       string            `json:"checksum_sha256,omitempty"`
}

// ListOptions represents options for listing files
type ListOptions struct {
	Delimiter         string `json:"delimiter,omitempty"`
	MaxKeys           int    `json:"max_keys,omitempty"`
	ContinuationToken string `json:"continuation_token,omitempty"`
}

// ListResult represents result of list operation
type ListResult struct {
	Files                 []*FileInfo `json:"files"`
	CommonPrefixes        []string    `json:"common_prefixes"`
	IsTruncated           bool        `json:"is_truncated"`
	NextContinuationToken string      `json:"next_continuation_token,omitempty"`
}

// FileUpload represents a file to be uploaded
type FileUpload struct {
	Key     string      `json:"key"`
	Reader  io.Reader   `json:"-"`
	Options *PutOptions `json:"options,omitempty"`
}

// ImageTransformations represents image processing options
type ImageTransformations struct {
	Width      int               `json:"width,omitempty"`
	Height     int               `json:"height,omitempty"`
	Quality    int               `json:"quality,omitempty"`
	Format     string            `json:"format,omitempty"`
	Resize     string            `json:"resize,omitempty"` // fit, fill, crop
	Rotate     int               `json:"rotate,omitempty"`
	Blur       float64           `json:"blur,omitempty"`
	Sharpen    float64           `json:"sharpen,omitempty"`
	Brightness float64           `json:"brightness,omitempty"`
	Contrast   float64           `json:"contrast,omitempty"`
	Saturation float64           `json:"saturation,omitempty"`
	Crop       *CropOptions      `json:"crop,omitempty"`
	Watermark  *WatermarkOptions `json:"watermark,omitempty"`
}

// CropOptions represents crop parameters
type CropOptions struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// WatermarkOptions represents watermark parameters
type WatermarkOptions struct {
	Image    string  `json:"image"`
	Text     string  `json:"text,omitempty"`
	Position string  `json:"position"` // top-left, top-right, bottom-left, bottom-right, center
	Opacity  float64 `json:"opacity"`
	Scale    float64 `json:"scale"`
}

// ImageInfo represents image metadata
type ImageInfo struct {
	Width       int     `json:"width"`
	Height      int     `json:"height"`
	Format      string  `json:"format"`
	ColorSpace  string  `json:"color_space"`
	HasAlpha    bool    `json:"has_alpha"`
	FileSize    int64   `json:"file_size"`
	AspectRatio float64 `json:"aspect_ratio"`
}

// MediaVariant represents different versions of media
type MediaVariant struct {
	Name   string `json:"name"`
	Key    string `json:"key"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
	Size   int64  `json:"size"`
	Format string `json:"format"`
}

// OptimizeOptions represents image optimization options
type OptimizeOptions struct {
	Quality     int    `json:"quality"`
	Progressive bool   `json:"progressive"`
	Strip       bool   `json:"strip"` // Remove metadata
	Lossless    bool   `json:"lossless"`
	Format      string `json:"format"`
	WebP        bool   `json:"webp"`
	AVIF        bool   `json:"avif"`
}

// VideoOptions represents video processing options
type VideoOptions struct {
	Codec     string `json:"codec"`
	Bitrate   int    `json:"bitrate"`
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	FPS       int    `json:"fps"`
	Format    string `json:"format"`
	Quality   string `json:"quality"` // low, medium, high
	StartTime int    `json:"start_time,omitempty"`
	Duration  int    `json:"duration,omitempty"`
}

// BucketPolicy represents bucket access policy
type BucketPolicy struct {
	Version    string      `json:"version"`
	Statements []Statement `json:"statements"`
}

// Statement represents policy statement
type Statement struct {
	Effect    string                 `json:"effect"` // Allow, Deny
	Principal map[string]interface{} `json:"principal"`
	Action    []string               `json:"action"`
	Resource  []string               `json:"resource"`
	Condition map[string]interface{} `json:"condition,omitempty"`
}

// MultipartUpload represents multipart upload session
type MultipartUpload struct {
	Key      string `json:"key"`
	UploadID string `json:"upload_id"`
	Bucket   string `json:"bucket,omitempty"`
}

// UploadPart represents uploaded part
type UploadPart struct {
	PartNumber int    `json:"part_number"`
	ETag       string `json:"etag"`
	Size       int64  `json:"size"`
}

// Config represents storage configuration
type Config struct {
	// Provider settings
	Provider string `json:"provider"` // local, s3, minio, gcs

	// Connection settings
	Endpoint  string `json:"endpoint,omitempty"`
	Region    string `json:"region,omitempty"`
	AccessKey string `json:"access_key,omitempty"`
	SecretKey string `json:"secret_key,omitempty"`
	Token     string `json:"token,omitempty"`

	// Bucket settings
	Bucket      string `json:"bucket,omitempty"`
	BasePath    string `json:"base_path,omitempty"`
	PublicURL   string `json:"public_url,omitempty"`
	CDNEndpoint string `json:"cdn_endpoint,omitempty"`

	// Local storage settings
	LocalPath string `json:"local_path,omitempty"`
	LocalURL  string `json:"local_url,omitempty"`

	// SSL settings
	UseSSL             bool `json:"use_ssl"`
	InsecureSkipVerify bool `json:"insecure_skip_verify"`

	// Default settings
	DefaultACL          string `json:"default_acl,omitempty"`
	DefaultStorageClass string `json:"default_storage_class,omitempty"`
	DefaultCacheControl string `json:"default_cache_control,omitempty"`

	// Media processing
	EnableImageProcessing bool            `json:"enable_image_processing"`
	ImageProcessor        string          `json:"image_processor"` // native, imagemagick, sharp
	ThumbnailSizes        []ThumbnailSize `json:"thumbnail_sizes"`

	// Upload settings
	MaxFileSize    int64    `json:"max_file_size"`
	AllowedTypes   []string `json:"allowed_types"`
	ForbiddenTypes []string `json:"forbidden_types"`
	RequireAuth    bool     `json:"require_auth"`

	// Performance settings
	PartSize       int64         `json:"part_size"` // For multipart uploads
	MaxConcurrency int           `json:"max_concurrency"`
	Timeout        time.Duration `json:"timeout"`
	RetryCount     int           `json:"retry_count"`
	RetryDelay     time.Duration `json:"retry_delay"`

	// Cleanup settings
	EnableCleanup   bool          `json:"enable_cleanup"`
	CleanupInterval time.Duration `json:"cleanup_interval"`
	MaxAge          time.Duration `json:"max_age"`

	// Advanced settings
	EnableMetrics     bool  `json:"enable_metrics"`
	EnableCompression bool  `json:"enable_compression"`
	EnableCaching     bool  `json:"enable_caching"`
	CacheSize         int64 `json:"cache_size"`
}

// ThumbnailSize represents thumbnail configuration
type ThumbnailSize struct {
	Name   string `json:"name"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Crop   bool   `json:"crop"`
}

// ThumbnailInfo represents thumbnail information
type ThumbnailInfo struct {
	Name   string `json:"name"`
	Key    string `json:"key"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Size   int64  `json:"size"`
	Format string `json:"format"`
	URL    string `json:"url"`
}

// OptimizationResult represents image optimization result
type OptimizationResult struct {
	OriginalSize   int64   `json:"original_size"`
	OptimizedSize  int64   `json:"optimized_size"`
	SavingsBytes   int64   `json:"savings_bytes"`
	SavingsPercent float64 `json:"savings_percent"`
	Format         string  `json:"format"`
	Quality        int     `json:"quality"`
}

// MediaMetadata represents media file metadata
type MediaMetadata struct {
	Title       string            `json:"title,omitempty"`
	Description string            `json:"description,omitempty"`
	Duration    int               `json:"duration,omitempty"` // in seconds
	Bitrate     int               `json:"bitrate,omitempty"`
	Width       int               `json:"width,omitempty"`
	Height      int               `json:"height,omitempty"`
	FPS         float64           `json:"fps,omitempty"`
	Codec       string            `json:"codec,omitempty"`
	Format      string            `json:"format,omitempty"`
	ColorSpace  string            `json:"color_space,omitempty"`
	AspectRatio string            `json:"aspect_ratio,omitempty"`
	Custom      map[string]string `json:"custom,omitempty"`
}

// PreviewOptions represents video preview options
type PreviewOptions struct {
	Timestamp int    `json:"timestamp"` // in seconds
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	Format    string `json:"format"`
	Quality   string `json:"quality"`
}

// PreviewInfo represents video preview information
type PreviewInfo struct {
	Key    string `json:"key"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Size   int64  `json:"size"`
	Format string `json:"format"`
	URL    string `json:"url"`
}

// ValidationResult represents media validation result
type ValidationResult struct {
	Valid       bool     `json:"valid"`
	Format      string   `json:"format"`
	Codec       string   `json:"codec"`
	Duration    int      `json:"duration,omitempty"`
	Bitrate     int      `json:"bitrate,omitempty"`
	Width       int      `json:"width,omitempty"`
	Height      int      `json:"height,omitempty"`
	Errors      []string `json:"errors,omitempty"`
	Warnings    []string `json:"warnings,omitempty"`
	FileSize    int64    `json:"file_size"`
	ChecksumMD5 string   `json:"checksum_md5,omitempty"`
}

// Storage types
const (
	StorageTypeLocal = "local"
	StorageTypeS3    = "s3"
	StorageTypeMinIO = "minio"
	StorageTypeGCS   = "gcs"
)

// Storage classes
const (
	StorageClassStandard           = "STANDARD"
	StorageClassReducedRedundancy  = "REDUCED_REDUNDANCY"
	StorageClassStandardIA         = "STANDARD_IA"
	StorageClassOnezoneIA          = "ONEZONE_IA"
	StorageClassIntelligentTiering = "INTELLIGENT_TIERING"
	StorageClassGlacier            = "GLACIER"
	StorageClassDeepArchive        = "DEEP_ARCHIVE"
)

// ACL types
const (
	ACLPrivate           = "private"
	ACLPublicRead        = "public-read"
	ACLPublicReadWrite   = "public-read-write"
	ACLAuthenticatedRead = "authenticated-read"
	ACLBucketOwnerRead   = "bucket-owner-read"
	ACLBucketOwnerFull   = "bucket-owner-full-control"
)

// Image formats
const (
	FormatJPEG = "jpeg"
	FormatPNG  = "png"
	FormatWebP = "webp"
	FormatAVIF = "avif"
	FormatGIF  = "gif"
	FormatTIFF = "tiff"
	FormatBMP  = "bmp"
)

// Resize modes
const (
	ResizeFit  = "fit"
	ResizeFill = "fill"
	ResizeCrop = "crop"
)

// Watermark positions
const (
	PositionTopLeft     = "top-left"
	PositionTopRight    = "top-right"
	PositionBottomLeft  = "bottom-left"
	PositionBottomRight = "bottom-right"
	PositionCenter      = "center"
)
