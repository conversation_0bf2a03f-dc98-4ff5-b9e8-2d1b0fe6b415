- internal/modules/auth/services/email_service.go không dùng thì xóa
- tham khảo module auth : tạo dto, sửa code liên quan.
- module auth cần phải sửa lại toàn bộ dto



./docs/cli-commands.md	./docs/cli/makefile-commands-reference.md	Trùng lặp. Cả hai đều liệt kê và giải thích các lệnh make. Nên hợp nhất thành một tài liệu tham chiếu duy nhất.
./docs/cli/cli-tools-architecture.md	./docs/cli/command-system-overview.md	Trùng lặp. Cả hai file đều mô tả kiến trúc của hệ thống CLI.
./docs/modules/media/api-file-management.md<br/>./docs/modules/media/api-folder-management.md<br/>./docs/modules/media/api-processing.md<br/>./docs/modules/media/api-search-filter.md	./docs/api/cms-api.md (phần Media Management)	Phân mảnh và Trùng lặp. Các file trong modules/media/ rất ngắn, chỉ liệt kê endpoints, trong khi cms-api.md đã có tài liệu chi tiết hơn về các API này. Các file nhỏ này nên được hợp nhất vào một tài liệu API hoàn chỉnh cho Media Module hoặc bị loại bỏ.


Dựa vào code hiện tại viết docs cho ./docs/modules/auth
+ Viết theo các đầu api : mermaid, flow


Dựa vào code hiện tại viết docs cho ./docs/modules/auth
+ Viết theo các đầu api : mermaid, flow


- bỏ fk_auth_sessions_tenant_id
- sửa CanAccessTenant, GetTenantMembership, GetActiveTenantIDs
- kiêm tra onboarding/status


sửa vào file cũ
internal/database/migrations/d_auth/311_add_redirect_urls_to_oauth_providers.down.sql
internal/database/migrations/d_auth/311_add_redirect_urls_to_oauth_providers.up.sql