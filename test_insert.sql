-- Test INSERT statement for auth_oauth_providers
INSERT INTO auth_oauth_providers (
    website_id,
    name,
    display_name,
    provider_type,
    client_id,
    client_secret,
    scopes,
    auth_url,
    token_url,
    user_info_url,
    is_enabled,
    is_default,
    auto_create_users,
    user_mapping
) VALUES (
    1,
    'google',
    'Google',
    'oauth2',
    'test-client-id',
    'test-client-secret',
    JSON_ARRAY('email', 'profile'),
    'https://accounts.google.com/o/oauth2/v2/auth',
    'https://oauth2.googleapis.com/token',
    'https://www.googleapis.com/oauth2/v2/userinfo',
    TRUE,
    FALSE,
    TRUE,
    JSON_OBJECT('id', 'id', 'email', 'email')
);
